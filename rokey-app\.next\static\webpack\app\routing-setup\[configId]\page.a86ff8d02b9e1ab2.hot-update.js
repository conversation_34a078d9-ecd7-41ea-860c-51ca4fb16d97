"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/routing-setup/[configId]/page",{

/***/ "(app-pages-browser)/./src/app/routing-setup/[configId]/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/routing-setup/[configId]/page.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RoutingSetupConfigPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowLeftIcon,ArrowUpIcon,Bars3Icon,BeakerIcon,BoltIcon,CheckCircleIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowLeftIcon,ArrowUpIcon,Bars3Icon,BeakerIcon,BoltIcon,CheckCircleIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowLeftIcon,ArrowUpIcon,Bars3Icon,BeakerIcon,BoltIcon,CheckCircleIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowLeftIcon,ArrowUpIcon,Bars3Icon,BeakerIcon,BoltIcon,CheckCircleIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowLeftIcon,ArrowUpIcon,Bars3Icon,BeakerIcon,BoltIcon,CheckCircleIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowLeftIcon,ArrowUpIcon,Bars3Icon,BeakerIcon,BoltIcon,CheckCircleIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowLeftIcon,ArrowUpIcon,Bars3Icon,BeakerIcon,BoltIcon,CheckCircleIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowLeftIcon,ArrowUpIcon,Bars3Icon,BeakerIcon,BoltIcon,CheckCircleIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowLeftIcon,ArrowUpIcon,Bars3Icon,BeakerIcon,BoltIcon,CheckCircleIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowLeftIcon,ArrowUpIcon,Bars3Icon,BeakerIcon,BoltIcon,CheckCircleIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowLeftIcon,ArrowUpIcon,Bars3Icon,BeakerIcon,BoltIcon,CheckCircleIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowLeftIcon.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/Reorder/Item.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/Reorder/Group.mjs\");\n/* harmony import */ var _hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useRoutingSetupPrefetch */ \"(app-pages-browser)/./src/hooks/useRoutingSetupPrefetch.ts\");\n/* harmony import */ var _components_RoutingSetupLoadingSkeleton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/RoutingSetupLoadingSkeleton */ \"(app-pages-browser)/./src/components/RoutingSetupLoadingSkeleton.tsx\");\n/* harmony import */ var _components_TierEnforcement__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/TierEnforcement */ \"(app-pages-browser)/./src/components/TierEnforcement/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst ROUTING_STRATEGIES = [\n    {\n        id: 'none',\n        name: 'Default Behavior',\n        shortDescription: 'Automatic load balancing',\n        description: \"RouKey will automatically load balance across all keys assigned to this configuration with intra-request retries. No extra setup needed.\",\n        icon: _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        id: 'intelligent_role',\n        name: 'Intelligent Role Routing',\n        shortDescription: 'AI-powered role classification',\n        description: \"RouKey uses an LLM to classify the user's prompt and routes to a key associated with that role. If no match, uses the 'Default General Chat Model'.\",\n        icon: _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        id: 'complexity_round_robin',\n        name: 'Complexity-Based Round-Robin',\n        shortDescription: 'Route by prompt complexity',\n        description: 'RouKey classifies prompt complexity (1-5) and round-robins among active keys assigned to that complexity. Searches proximal levels if no exact match.',\n        icon: _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        id: 'strict_fallback',\n        name: 'Strict Fallback',\n        shortDescription: 'Ordered failover sequence',\n        description: 'Define an ordered list of API keys. RouKey will try them in sequence until one succeeds.',\n        icon: _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        id: 'cost_optimized',\n        name: 'Cost-Optimized Routing',\n        shortDescription: 'Smart cost-performance balance',\n        description: 'RouKey intelligently balances cost and performance by routing simple tasks to cheaper models and complex tasks to premium models. Maximizes savings while ensuring quality.',\n        icon: _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        id: 'ab_routing',\n        name: 'A/B Routing',\n        shortDescription: 'Continuous model optimization',\n        description: 'RouKey continuously tests different models with 15% of requests to find the best performing models for your specific use cases. Automatically optimizes routing based on quality and cost metrics.',\n        icon: _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    }\n];\n// Draggable API Key Item Component for Strict Fallback\nfunction DraggableApiKeyItem(param) {\n    let { apiKey, index } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.ReorderItem, {\n        value: apiKey,\n        className: \"bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 shadow-sm transition-all duration-200 hover:shadow-md hover:border-gray-600/50 cursor-grab active:cursor-grabbing backdrop-blur-sm\",\n        whileDrag: {\n            scale: 1.02,\n            boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.3)\",\n            zIndex: 1000\n        },\n        dragListener: true,\n        dragControls: undefined,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-orange-500/20 border border-orange-500/30 rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-semibold text-orange-400\",\n                                children: index + 1\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-white\",\n                                    children: apiKey.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: [\n                                        apiKey.provider,\n                                        \" - \",\n                                        apiKey.predefined_model_id\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-2 text-gray-400 hover:text-gray-300 transition-colors cursor-grab active:cursor-grabbing\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n_c = DraggableApiKeyItem;\n// Simplified API Key Item Component (for non-draggable lists)\nfunction SimpleApiKeyItem(param) {\n    let { apiKey, index, onMoveUp, onMoveDown } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        className: \"bg-white border border-gray-200 rounded-lg p-4 shadow-sm transition-all duration-200 hover:shadow-md hover:border-gray-300\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-orange-50 border border-orange-200 rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-semibold text-orange-600\",\n                                children: index + 1\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-gray-900\",\n                                    children: apiKey.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600\",\n                                    children: [\n                                        apiKey.provider,\n                                        \" - \",\n                                        apiKey.predefined_model_id\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-1\",\n                    children: [\n                        onMoveUp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onMoveUp,\n                            className: \"p-1 text-gray-400 hover:text-orange-600 hover:bg-orange-50 rounded transition-all duration-200\",\n                            title: \"Move up\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, this),\n                        onMoveDown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onMoveDown,\n                            className: \"p-1 text-gray-400 hover:text-orange-600 hover:bg-orange-50 rounded transition-all duration-200\",\n                            title: \"Move down\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SimpleApiKeyItem;\nfunction RoutingSetupConfigPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const configId = params.configId;\n    // Prefetch hook\n    const { getCachedData, isCached } = (0,_hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_4__.useRoutingSetupPrefetch)();\n    // Smart back navigation using search params and fallback to referrer\n    const getBackUrl = ()=>{\n        // First, check if we have a 'from' parameter in the URL\n        const fromParam = searchParams.get('from');\n        if (fromParam === 'routing-setup') {\n            return '/routing-setup';\n        }\n        if (fromParam === 'model-config') {\n            return \"/my-models/\".concat(configId);\n        }\n        // Fallback to referrer detection for backward compatibility\n        if (true) {\n            const referrer = document.referrer;\n            const currentHost = window.location.host;\n            if (referrer && referrer.includes(currentHost)) {\n                try {\n                    const referrerUrl = new URL(referrer);\n                    const referrerPath = referrerUrl.pathname;\n                    // If they came from the main routing setup page (exact match)\n                    if (referrerPath === '/routing-setup') {\n                        return '/routing-setup';\n                    }\n                    // If they came from the model configuration page\n                    if (referrerPath === \"/my-models/\".concat(configId)) {\n                        return \"/my-models/\".concat(configId);\n                    }\n                    // If they came from the my-models list page\n                    if (referrerPath === '/my-models') {\n                        return \"/my-models/\".concat(configId);\n                    }\n                    // If they came from any other my-models page, go to this config's page\n                    if (referrerPath.startsWith('/my-models/') && !referrerPath.includes('/routing-setup')) {\n                        return \"/my-models/\".concat(configId);\n                    }\n                } catch (e) {\n                // Silently handle URL parsing errors\n                }\n            }\n        }\n        // Default fallback to model configuration page\n        return \"/my-models/\".concat(configId);\n    };\n    const getBackButtonText = ()=>{\n        // First, check if we have a 'from' parameter in the URL\n        const fromParam = searchParams.get('from');\n        if (fromParam === 'routing-setup') {\n            return 'Back to Routing Setup';\n        }\n        if (fromParam === 'model-config') {\n            return 'Back to Configuration';\n        }\n        // Fallback to referrer detection\n        if (true) {\n            const referrer = document.referrer;\n            const currentHost = window.location.host;\n            if (referrer && referrer.includes(currentHost)) {\n                try {\n                    const referrerUrl = new URL(referrer);\n                    const referrerPath = referrerUrl.pathname;\n                    if (referrerPath === '/routing-setup') {\n                        return 'Back to Routing Setup';\n                    }\n                    if (referrerPath === \"/my-models/\".concat(configId) || referrerPath === '/my-models' || referrerPath.startsWith('/my-models/')) {\n                        return 'Back to Configuration';\n                    }\n                } catch (e) {\n                // Silently handle URL parsing errors\n                }\n            }\n        }\n        // Default fallback text\n        return 'Back to Configuration';\n    };\n    const handleBackNavigation = ()=>{\n        if ( true && window.history.length > 1) {\n            // Try to use browser back if there's history\n            const backUrl = getBackUrl();\n            const referrer = document.referrer;\n            const currentHost = window.location.host;\n            // If referrer is from our app, use browser back for better UX\n            if (referrer && referrer.includes(currentHost)) {\n                router.back();\n                return;\n            }\n        }\n        // Fallback to programmatic navigation\n        router.push(getBackUrl());\n    };\n    const [configDetails, setConfigDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOptimisticLoading, setShowOptimisticLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedStrategy, setSelectedStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('none');\n    const [strategyParams, setStrategyParams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [availableApiKeys, setAvailableApiKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingApiKeys, setIsLoadingApiKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State specifically for strict_fallback strategy\n    const [orderedFallbackKeys, setOrderedFallbackKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // New State for Complexity-Based Round-Robin\n    const [selectedApiKeyForComplexity, setSelectedApiKeyForComplexity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Stores fetched assignments: { apiKeyId1: [1,2], apiKeyId2: [3] }\n    const [complexityAssignments, setComplexityAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Levels selected in UI for the current selectedApiKeyForComplexity\n    const [currentKeyComplexityLevels, setCurrentKeyComplexityLevels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isFetchingAssignments, setIsFetchingAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSavingAssignments, setIsSavingAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [assignmentsError, setAssignmentsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [assignmentsSuccessMessage, setAssignmentsSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Temporarily disabled DnD sensors and handlers\n    // const sensors = useSensors(\n    //   useSensor(PointerSensor),\n    //   useSensor(KeyboardSensor, {\n    //     coordinateGetter: sortableKeyboardCoordinates,\n    //   })\n    // );\n    // const handleDragEnd = (event: DragEndEvent) => {\n    //   const { active, over } = event;\n    //   if (active.id !== over?.id) {\n    //     const oldIndex = orderedFallbackKeys.findIndex((key) => key.id === active.id);\n    //     const newIndex = orderedFallbackKeys.findIndex((key) => key.id === over?.id);\n    //     const newOrderedKeys = arrayMove(orderedFallbackKeys, oldIndex, newIndex);\n    //     setOrderedFallbackKeys(newOrderedKeys);\n    //     // Update strategyParams immediately for saving\n    //     setStrategyParams({ ordered_api_key_ids: newOrderedKeys.map(k => k.id) });\n    //   }\n    // };\n    const fetchConfigAndKeys = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys]\": async ()=>{\n            if (!configId) {\n                setError('Configuration ID is missing.');\n                setIsLoading(false);\n                return;\n            }\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.configDetails && cachedData.apiKeys) {\n                var _cachedData_routingParams;\n                console.log(\"⚡ [ROUTING SETUP] Using cached data for config: \".concat(configId));\n                setConfigDetails(cachedData.configDetails);\n                setAvailableApiKeys(cachedData.apiKeys);\n                const currentSelectedStrategy = cachedData.routingStrategy || 'none';\n                setSelectedStrategy(currentSelectedStrategy);\n                setStrategyParams(cachedData.routingParams || {});\n                // Initialize orderedFallbackKeys based on cached data\n                if (currentSelectedStrategy === 'strict_fallback' && ((_cachedData_routingParams = cachedData.routingParams) === null || _cachedData_routingParams === void 0 ? void 0 : _cachedData_routingParams.ordered_api_key_ids)) {\n                    const savedOrder = cachedData.routingParams.ordered_api_key_ids;\n                    const reorderedKeys = savedOrder.map({\n                        \"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].reorderedKeys\": (id)=>cachedData.apiKeys.find({\n                                \"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].reorderedKeys\": (k)=>k.id === id\n                            }[\"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].reorderedKeys\"])\n                    }[\"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].reorderedKeys\"]).filter(Boolean);\n                    const remainingKeys = cachedData.apiKeys.filter({\n                        \"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].remainingKeys\": (k)=>!savedOrder.includes(k.id)\n                    }[\"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].remainingKeys\"]);\n                    setOrderedFallbackKeys([\n                        ...reorderedKeys,\n                        ...remainingKeys\n                    ]);\n                } else {\n                    setOrderedFallbackKeys([\n                        ...cachedData.apiKeys\n                    ]);\n                }\n                setIsLoading(false);\n                setIsLoadingApiKeys(false);\n                return;\n            }\n            // Show optimistic loading for first-time visits\n            if (!isCached(configId)) {\n                setShowOptimisticLoading(true);\n            }\n            setIsLoading(true);\n            setIsLoadingApiKeys(true);\n            setError(null);\n            setSuccessMessage(null);\n            try {\n                // Fetch config details\n                const configRes = await fetch(\"/api/custom-configs/\".concat(configId));\n                if (!configRes.ok) {\n                    const errData = await configRes.json();\n                    throw new Error(errData.error || 'Failed to fetch configuration');\n                }\n                const currentConfig = await configRes.json();\n                setConfigDetails(currentConfig);\n                const currentSelectedStrategy = currentConfig.routing_strategy || 'none';\n                setSelectedStrategy(currentSelectedStrategy);\n                const currentStrategyParams = currentConfig.routing_strategy_params || {};\n                setStrategyParams(currentStrategyParams); // General params state\n                // Fetch API keys for this configuration\n                const keysRes = await fetch(\"/api/keys?custom_config_id=\".concat(configId));\n                if (!keysRes.ok) {\n                    const errData = await keysRes.json();\n                    throw new Error(errData.error || 'Failed to fetch API keys for this configuration');\n                }\n                const keys = await keysRes.json();\n                setAvailableApiKeys(keys);\n                // Initialize orderedFallbackKeys based on fetched keys and saved params\n                if (currentSelectedStrategy === 'strict_fallback' && currentStrategyParams.ordered_api_key_ids) {\n                    const savedOrder = currentStrategyParams.ordered_api_key_ids;\n                    const reorderedKeys = savedOrder.map({\n                        \"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].reorderedKeys\": (id)=>keys.find({\n                                \"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].reorderedKeys\": (k)=>k.id === id\n                            }[\"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].reorderedKeys\"])\n                    }[\"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].reorderedKeys\"]).filter(Boolean);\n                    // Add any keys present in `keys` but not in `savedOrder` to the end\n                    const remainingKeys = keys.filter({\n                        \"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].remainingKeys\": (k)=>!savedOrder.includes(k.id)\n                    }[\"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].remainingKeys\"]);\n                    setOrderedFallbackKeys([\n                        ...reorderedKeys,\n                        ...remainingKeys\n                    ]);\n                } else {\n                    setOrderedFallbackKeys([\n                        ...keys\n                    ]); // Default order or for other strategies initially\n                }\n            } catch (err) {\n                setError(\"Error loading data: \".concat(err.message));\n                setConfigDetails(null);\n                setAvailableApiKeys([]);\n            } finally{\n                setIsLoading(false);\n                setIsLoadingApiKeys(false);\n                setShowOptimisticLoading(false);\n            }\n        }\n    }[\"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys]\"], [\n        configId,\n        getCachedData,\n        isCached\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutingSetupConfigPage.useEffect\": ()=>{\n            fetchConfigAndKeys();\n        }\n    }[\"RoutingSetupConfigPage.useEffect\"], [\n        fetchConfigAndKeys\n    ]);\n    // Fetch complexity assignments for the selected API key\n    const fetchComplexityAssignmentsForKey = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RoutingSetupConfigPage.useCallback[fetchComplexityAssignmentsForKey]\": async (apiKeyId)=>{\n            if (!configId || !apiKeyId) return;\n            setIsFetchingAssignments(true);\n            setAssignmentsError(null);\n            setAssignmentsSuccessMessage(null);\n            try {\n                const response = await fetch(\"/api/custom-configs/\".concat(configId, \"/keys/\").concat(apiKeyId, \"/complexity-assignments\"));\n                if (!response.ok) {\n                    const errorData = await response.json();\n                    throw new Error(errorData.error || 'Failed to fetch complexity assignments');\n                }\n                const fetchedLevels = await response.json();\n                setComplexityAssignments({\n                    \"RoutingSetupConfigPage.useCallback[fetchComplexityAssignmentsForKey]\": (prev)=>({\n                            ...prev,\n                            [apiKeyId]: fetchedLevels\n                        })\n                }[\"RoutingSetupConfigPage.useCallback[fetchComplexityAssignmentsForKey]\"]);\n                setCurrentKeyComplexityLevels(fetchedLevels);\n            } catch (err) {\n                setAssignmentsError(\"Error fetching assignments for key: \".concat(err.message));\n                setCurrentKeyComplexityLevels([]); // Reset on error\n            } finally{\n                setIsFetchingAssignments(false);\n            }\n        }\n    }[\"RoutingSetupConfigPage.useCallback[fetchComplexityAssignmentsForKey]\"], [\n        configId\n    ]);\n    // Effect to fetch assignments when selectedApiKeyForComplexity changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutingSetupConfigPage.useEffect\": ()=>{\n            if (selectedApiKeyForComplexity) {\n                fetchComplexityAssignmentsForKey(selectedApiKeyForComplexity);\n            } else {\n                // Clear levels if no key is selected\n                setCurrentKeyComplexityLevels([]);\n                setAssignmentsError(null);\n            }\n        }\n    }[\"RoutingSetupConfigPage.useEffect\"], [\n        selectedApiKeyForComplexity,\n        fetchComplexityAssignmentsForKey\n    ]);\n    // Handle checkbox change for complexity levels\n    const handleComplexityLevelChange = (level, checked)=>{\n        setCurrentKeyComplexityLevels((prevLevels)=>{\n            if (checked) {\n                return [\n                    ...prevLevels,\n                    level\n                ].sort((a, b)=>a - b);\n            } else {\n                return prevLevels.filter((l)=>l !== level);\n            }\n        });\n    };\n    // Save complexity assignments for the selected API key\n    const handleSaveComplexityAssignments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RoutingSetupConfigPage.useCallback[handleSaveComplexityAssignments]\": async ()=>{\n            if (!configId || !selectedApiKeyForComplexity) {\n                setAssignmentsError('No API key selected to save assignments for.');\n                return;\n            }\n            setIsSavingAssignments(true);\n            setAssignmentsError(null);\n            setAssignmentsSuccessMessage(null);\n            try {\n                const response = await fetch(\"/api/custom-configs/\".concat(configId, \"/keys/\").concat(selectedApiKeyForComplexity, \"/complexity-assignments\"), {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        complexity_levels: currentKeyComplexityLevels\n                    })\n                });\n                if (!response.ok) {\n                    const errorData = await response.json();\n                    throw new Error(errorData.error || 'Failed to save complexity assignments');\n                }\n                const result = await response.json();\n                // Update the main store of assignments for this key\n                setComplexityAssignments({\n                    \"RoutingSetupConfigPage.useCallback[handleSaveComplexityAssignments]\": (prev)=>({\n                            ...prev,\n                            [selectedApiKeyForComplexity]: [\n                                ...currentKeyComplexityLevels\n                            ]\n                        })\n                }[\"RoutingSetupConfigPage.useCallback[handleSaveComplexityAssignments]\"]);\n                setAssignmentsSuccessMessage(result.message || 'Complexity assignments saved successfully!');\n            } catch (err) {\n                setAssignmentsError(\"Error saving assignments: \".concat(err.message));\n            } finally{\n                setIsSavingAssignments(false);\n            }\n        }\n    }[\"RoutingSetupConfigPage.useCallback[handleSaveComplexityAssignments]\"], [\n        configId,\n        selectedApiKeyForComplexity,\n        currentKeyComplexityLevels\n    ]);\n    // Handle drag and drop reordering for strict fallback\n    const handleDragReorder = (newOrder)=>{\n        setOrderedFallbackKeys(newOrder);\n        // Update strategyParams immediately for saving\n        setStrategyParams({\n            ordered_api_key_ids: newOrder.map((k)=>k.id)\n        });\n    };\n    const moveFallbackKey = (index, direction)=>{\n        const newOrderedKeys = [\n            ...orderedFallbackKeys\n        ];\n        const keyToMove = newOrderedKeys[index];\n        if (direction === 'up' && index > 0) {\n            newOrderedKeys.splice(index, 1);\n            newOrderedKeys.splice(index - 1, 0, keyToMove);\n        } else if (direction === 'down' && index < newOrderedKeys.length - 1) {\n            newOrderedKeys.splice(index, 1);\n            newOrderedKeys.splice(index + 1, 0, keyToMove);\n        }\n        setOrderedFallbackKeys(newOrderedKeys);\n        // Update strategyParams immediately for saving\n        setStrategyParams({\n            ordered_api_key_ids: newOrderedKeys.map((k)=>k.id)\n        });\n    };\n    const handleSaveRoutingSettings = async (e)=>{\n        e.preventDefault();\n        if (!configId || !configDetails) {\n            setError('Configuration details not loaded.');\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        setSuccessMessage(null);\n        let paramsToSave = strategyParams;\n        if (selectedStrategy === 'strict_fallback') {\n            paramsToSave = {\n                ordered_api_key_ids: orderedFallbackKeys.map((k)=>k.id)\n            };\n        }\n        // Add similar logic for other strategies to structure their paramsToSave\n        try {\n            const response = await fetch(\"/api/custom-configs/\".concat(configId, \"/routing\"), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    routing_strategy: selectedStrategy,\n                    routing_strategy_params: paramsToSave\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Failed to save routing settings');\n            }\n            const result = await response.json();\n            setSuccessMessage(result.message || 'Routing settings saved successfully!');\n            setConfigDetails((prev)=>prev ? {\n                    ...prev,\n                    routing_strategy: selectedStrategy,\n                    routing_strategy_params: paramsToSave\n                } : null);\n            // Update local params to reflect saved state if necessary, e.g. if backend transforms it\n            setStrategyParams(paramsToSave);\n        } catch (err) {\n            setError(\"Error saving settings: \".concat(err.message));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const renderComplexityAssignmentUI = ()=>{\n        var _availableApiKeys_find;\n        if (selectedStrategy !== 'complexity_round_robin') return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mt-8 pt-6 border-t border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                    children: \"Complexity-Based Key Assignments\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 593,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-600 mb-6\",\n                    children: \"Assign API keys to handle prompts of specific complexity levels (1-5). The system will classify incoming prompts and round-robin requests among keys assigned to that complexity.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 594,\n                    columnNumber: 9\n                }, this),\n                isLoadingApiKeys && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 600,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 ml-2\",\n                            children: \"Loading API keys...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 601,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 599,\n                    columnNumber: 11\n                }, this),\n                !isLoadingApiKeys && availableApiKeys.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-yellow-800\",\n                        children: \"No API keys found for this configuration. Please add API keys first on the model configuration page.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 606,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 605,\n                    columnNumber: 11\n                }, this),\n                availableApiKeys.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"apiKeyForComplexity\",\n                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                            children: \"Select API Key to Assign Complexities:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 612,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            id: \"apiKeyForComplexity\",\n                            value: selectedApiKeyForComplexity || '',\n                            onChange: (e)=>setSelectedApiKeyForComplexity(e.target.value || null),\n                            className: \"form-select max-w-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    disabled: true,\n                                    children: \"-- Select an API Key --\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 15\n                                }, this),\n                                availableApiKeys.map((key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: key.id,\n                                        children: [\n                                            key.label,\n                                            \" (\",\n                                            key.provider,\n                                            \" - \",\n                                            key.predefined_model_id,\n                                            \")\"\n                                        ]\n                                    }, key.id, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 17\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 613,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 611,\n                    columnNumber: 11\n                }, this),\n                selectedApiKeyForComplexity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-md font-medium text-gray-900 mb-4\",\n                            children: [\n                                \"Assign Complexity Levels for: \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-orange-600\",\n                                    children: (_availableApiKeys_find = availableApiKeys.find((k)=>k.id === selectedApiKeyForComplexity)) === null || _availableApiKeys_find === void 0 ? void 0 : _availableApiKeys_find.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 98\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 629,\n                            columnNumber: 13\n                        }, this),\n                        isFetchingAssignments && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 ml-2\",\n                                    children: \"Loading current assignments...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 632,\n                            columnNumber: 15\n                        }, this),\n                        assignmentsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-3 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-800 text-sm\",\n                                children: assignmentsError\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 640,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 639,\n                            columnNumber: 15\n                        }, this),\n                        assignmentsSuccessMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-50 border border-green-200 rounded-lg p-3 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-800 text-sm\",\n                                children: assignmentsSuccessMessage\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 646,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 645,\n                            columnNumber: 15\n                        }, this),\n                        !isFetchingAssignments && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 mb-6\",\n                            children: [\n                                1,\n                                2,\n                                3,\n                                4,\n                                5\n                            ].map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center space-x-3 p-3 bg-gray-50 border border-gray-200 rounded-lg hover:border-gray-300 cursor-pointer transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: currentKeyComplexityLevels.includes(level),\n                                            onChange: (e)=>handleComplexityLevelChange(level, e.target.checked),\n                                            className: \"h-4 w-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500 focus:ring-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 654,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-900\",\n                                            children: [\n                                                \"Complexity Level \",\n                                                level\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 660,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, level, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 651,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSaveComplexityAssignments,\n                            disabled: isSavingAssignments || isFetchingAssignments,\n                            className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: isSavingAssignments ? 'Saving Assignments...' : 'Save Assignments for this Key'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 666,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 628,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n            lineNumber: 592,\n            columnNumber: 7\n        }, this);\n    };\n    // Render configuration content based on selected strategy\n    const renderConfigurationContent = ()=>{\n        const selectedStrategyData = ROUTING_STRATEGIES.find((s)=>s.id === selectedStrategy);\n        if (selectedStrategy === 'none') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-20 h-20 bg-orange-500/20 rounded-full flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-10 h-10 text-orange-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 687,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 686,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-bold text-white mb-4\",\n                        children: \"Default Behavior\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 689,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 max-w-md mx-auto leading-relaxed\",\n                        children: selectedStrategyData === null || selectedStrategyData === void 0 ? void 0 : selectedStrategyData.description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 690,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 p-4 bg-green-900/20 border border-green-500/30 rounded-xl backdrop-blur-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"w-5 h-5 text-green-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 695,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-300 font-medium\",\n                                    children: \"No additional setup required\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 696,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 694,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 693,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 pt-6 border-t border-gray-700/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                            disabled: isLoading,\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2 animate-spin\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 709,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Saving...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 716,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Save Routing Settings\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 702,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 701,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                lineNumber: 685,\n                columnNumber: 9\n            }, this);\n        }\n        if (selectedStrategy === 'intelligent_role') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-white mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-7 h-7 mr-3 text-orange-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 731,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Intelligent Role Routing\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 730,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 leading-relaxed\",\n                                children: selectedStrategyData === null || selectedStrategyData === void 0 ? void 0 : selectedStrategyData.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 734,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 729,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-900/20 border border-blue-500/30 rounded-xl p-6 backdrop-blur-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-blue-300 mb-3\",\n                                        children: \"How it works:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 741,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 text-sm text-blue-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-bold text-blue-400\",\n                                                            children: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 745,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"System analyzes your prompt to understand the main task\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 747,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 743,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-bold text-blue-400\",\n                                                            children: \"2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 751,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 750,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Matches task to relevant RoKey operational roles (e.g., 'Coding - Frontend', 'Copywriting')\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 753,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 749,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-bold text-blue-400\",\n                                                            children: \"3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 757,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 756,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Routes to assigned API key or falls back to 'Default General Chat Model'\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 755,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 740,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-900/20 border border-green-500/30 rounded-xl p-6 backdrop-blur-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"w-6 h-6 text-green-400 mt-0.5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 766,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-green-300 mb-2\",\n                                                    children: \"Ready to use!\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 768,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-green-200 leading-relaxed\",\n                                                    children: \"No additional setup required. Future enhancements may allow further customization.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 769,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 767,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 765,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 764,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 739,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 pt-6 border-t border-gray-700/50 flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                            disabled: isLoading,\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2 animate-spin\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 787,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 786,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Saving...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 793,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Save Routing Settings\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 779,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 778,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                lineNumber: 728,\n                columnNumber: 9\n            }, this);\n        }\n        if (selectedStrategy === 'strict_fallback') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-white mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-7 h-7 mr-3 text-orange-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 808,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Strict Fallback Configuration\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 807,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 leading-relaxed\",\n                                children: selectedStrategyData === null || selectedStrategyData === void 0 ? void 0 : selectedStrategyData.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 811,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 806,\n                        columnNumber: 11\n                    }, this),\n                    isLoadingApiKeys && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 border-2 border-orange-400/20 border-t-orange-400 rounded-full animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 818,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 ml-3\",\n                                children: \"Loading API keys...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 819,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 817,\n                        columnNumber: 13\n                    }, this),\n                    !isLoadingApiKeys && availableApiKeys.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-900/20 border border-yellow-500/30 rounded-xl p-8 text-center backdrop-blur-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-yellow-400\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 827,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 826,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 825,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-yellow-300 mb-2\",\n                                children: \"No API Keys Found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 830,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-yellow-200 leading-relaxed\",\n                                children: \"Please add API keys on the main configuration page to set up fallback order.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 831,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 824,\n                        columnNumber: 13\n                    }, this),\n                    !isLoadingApiKeys && availableApiKeys.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-900/20 border border-blue-500/30 rounded-xl p-6 backdrop-blur-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-blue-400\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 843,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 842,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 841,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-200 leading-relaxed\",\n                                            children: \"Drag and drop to arrange the API keys in the desired order of execution. The router will try the first key, then the second if the first fails, and so on.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 846,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 840,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 839,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.ReorderGroup, {\n                                axis: \"y\",\n                                values: orderedFallbackKeys,\n                                onReorder: handleDragReorder,\n                                className: \"space-y-3\",\n                                children: orderedFallbackKeys.map((apiKey, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DraggableApiKeyItem, {\n                                        apiKey: apiKey,\n                                        index: index\n                                    }, apiKey.id, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 860,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 853,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 838,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 pt-6 border-t border-gray-700/50 flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                            disabled: isLoading || availableApiKeys.length === 0,\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2 animate-spin\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 880,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 879,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Saving...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 886,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Save Routing Settings\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 872,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 871,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                lineNumber: 805,\n                columnNumber: 9\n            }, this);\n        }\n        if (selectedStrategy === 'complexity_round_robin') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-white mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-7 h-7 mr-3 text-orange-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 901,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Complexity-Based Round-Robin\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 900,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 leading-relaxed\",\n                                children: selectedStrategyData === null || selectedStrategyData === void 0 ? void 0 : selectedStrategyData.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 904,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 899,\n                        columnNumber: 11\n                    }, this),\n                    renderComplexityAssignmentUI(),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 pt-6 border-t border-gray-700/50 flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                            disabled: isLoading,\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2 animate-spin\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 921,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 920,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Saving...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 927,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Save Routing Settings\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 913,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 912,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                lineNumber: 898,\n                columnNumber: 9\n            }, this);\n        }\n        if (selectedStrategy === 'cost_optimized') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-white mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-7 h-7 mr-3 text-orange-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 942,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Smart Cost-Optimized Routing\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 941,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 leading-relaxed\",\n                                children: selectedStrategyData === null || selectedStrategyData === void 0 ? void 0 : selectedStrategyData.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 945,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 940,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-900/20 border border-blue-500/30 rounded-xl p-6 backdrop-blur-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-blue-300 mb-3\",\n                                        children: \"How it works:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 952,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 text-sm text-blue-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-bold text-blue-400\",\n                                                            children: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 956,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 955,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"RouKey Classifier\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 958,\n                                                                columnNumber: 22\n                                                            }, this),\n                                                            \" analyzes your prompt to determine task complexity (Simple, Moderate, Complex)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 958,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 954,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-bold text-blue-400\",\n                                                            children: \"2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 962,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 961,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Routes to appropriate cost tier: \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Simple tasks\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 964,\n                                                                columnNumber: 55\n                                                            }, this),\n                                                            \" → Cheapest models, \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Complex tasks\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 964,\n                                                                columnNumber: 104\n                                                            }, this),\n                                                            \" → Premium models\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 964,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 960,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-bold text-blue-400\",\n                                                            children: \"3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 968,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 967,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Prioritizes cost savings by using cheaper models whenever possible while ensuring quality for complex tasks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 970,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 966,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 953,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 951,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-900/20 border border-green-500/30 rounded-xl p-4 backdrop-blur-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-green-400 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 978,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                        className: \"font-semibold text-green-300 text-sm\",\n                                                        children: \"Simple Tasks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 979,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 977,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-green-200 leading-relaxed\",\n                                                children: [\n                                                    \"Basic questions, simple conversations, straightforward requests → \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Cheapest models\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 982,\n                                                        columnNumber: 85\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 981,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 976,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-900/20 border border-yellow-500/30 rounded-xl p-4 backdrop-blur-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-yellow-400 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 988,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                        className: \"font-semibold text-yellow-300 text-sm\",\n                                                        children: \"Moderate Tasks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 989,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 987,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-yellow-200 leading-relaxed\",\n                                                children: [\n                                                    \"Analysis, explanations, moderate complexity → \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Balanced pricing models\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 992,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 991,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 986,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-900/20 border border-purple-500/30 rounded-xl p-4 backdrop-blur-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-purple-400 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 998,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                        className: \"font-semibold text-purple-300 text-sm\",\n                                                        children: \"Complex Tasks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 999,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 997,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-purple-200 leading-relaxed\",\n                                                children: [\n                                                    \"Advanced reasoning, coding, research → \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Premium models\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1002,\n                                                        columnNumber: 58\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1001,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 996,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 975,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-900/20 border border-orange-500/30 rounded-xl p-6 backdrop-blur-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-6 h-6 text-orange-400 mt-0.5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1009,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-orange-300 mb-2\",\n                                                    children: \"Intelligent Cost Optimization\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1011,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-orange-200 leading-relaxed\",\n                                                    children: \"This strategy maximizes cost savings by routing most requests to cheaper models, while automatically upgrading to premium models only when task complexity truly requires it. Perfect for balancing budget constraints with quality requirements.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1012,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1010,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1008,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1007,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-900/20 border border-green-500/30 rounded-xl p-6 backdrop-blur-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"w-6 h-6 text-green-400 mt-0.5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1021,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-green-300 mb-2\",\n                                                    children: \"Ready to use!\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1023,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-green-200 leading-relaxed\",\n                                                    children: \"No additional setup required. RouKey will automatically classify task complexity and route to the most cost-effective model tier for optimal savings and performance.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1024,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1022,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1020,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1019,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 950,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 pt-6 border-t border-gray-700/50 flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                            disabled: isLoading,\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2 animate-spin\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1042,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1041,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Saving...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1048,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Save Routing Settings\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1034,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1033,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                lineNumber: 939,\n                columnNumber: 9\n            }, this);\n        }\n        if (selectedStrategy === 'ab_routing') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-white mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-7 h-7 mr-3 text-orange-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1063,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"A/B Routing Optimization\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1062,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 leading-relaxed\",\n                                children: selectedStrategyData === null || selectedStrategyData === void 0 ? void 0 : selectedStrategyData.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1066,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1061,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-900/20 border border-blue-500/30 rounded-xl p-6 backdrop-blur-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-blue-300 mb-3\",\n                                        children: \"How it works:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1073,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 text-sm text-blue-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-bold text-blue-400\",\n                                                            children: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1077,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1076,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"85% Control Group:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1079,\n                                                                columnNumber: 22\n                                                            }, this),\n                                                            \" Routes to your best-performing models based on historical data\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1079,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1075,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-bold text-blue-400\",\n                                                            children: \"2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1083,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1082,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"15% Test Group:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1085,\n                                                                columnNumber: 22\n                                                            }, this),\n                                                            \" Experiments with different models to discover better options\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1085,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1081,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-bold text-blue-400\",\n                                                            children: \"3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1089,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1088,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Continuous Learning:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1091,\n                                                                columnNumber: 22\n                                                            }, this),\n                                                            \" Automatically updates routing based on quality metrics and user feedback\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1091,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1087,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1074,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1072,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 border border-green-200 rounded-xl p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-green-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1099,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                        className: \"font-semibold text-green-900 text-sm\",\n                                                        children: \"Quality Optimization\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1100,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1098,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-green-800 leading-relaxed\",\n                                                children: \"Tracks response quality, user satisfaction, and task completion rates to identify the best models for your specific use cases.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1102,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1097,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 border border-purple-200 rounded-xl p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-purple-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1109,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                        className: \"font-semibold text-purple-900 text-sm\",\n                                                        children: \"Cost Efficiency\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1110,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1108,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-purple-800 leading-relaxed\",\n                                                children: \"Balances quality with cost to find models that deliver the best value for your specific requirements and budget.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1112,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1107,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1096,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 border border-orange-200 rounded-xl p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-6 h-6 text-orange-600 mt-0.5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1120,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-orange-900 mb-2\",\n                                                    children: \"Intelligent Experimentation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1122,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-orange-800 leading-relaxed\",\n                                                    children: \"This strategy continuously learns from your usage patterns to optimize routing decisions. The more you use it, the better it becomes at selecting the perfect model for each request.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1123,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1121,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1119,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1118,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 rounded-xl p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"w-6 h-6 text-green-600 mt-0.5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1132,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-green-900 mb-2\",\n                                                    children: \"Ready to use!\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1134,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-green-800 leading-relaxed\",\n                                                    children: \"No additional setup required. RouKey will automatically start A/B testing and learning from your requests to optimize routing performance.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1135,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1133,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1131,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1130,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1071,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 pt-6 border-t border-gray-200 flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                            disabled: isLoading,\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2 animate-spin\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1153,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1152,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Saving...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1159,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Save Routing Settings\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1145,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1144,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                lineNumber: 1060,\n                columnNumber: 9\n            }, this);\n        }\n        return null;\n    };\n    // Main render logic with optimistic loading\n    if (showOptimisticLoading && !isCached(configId)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoutingSetupLoadingSkeleton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1174,\n            columnNumber: 12\n        }, this);\n    }\n    if (isLoading && !configDetails) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoutingSetupLoadingSkeleton__WEBPACK_IMPORTED_MODULE_5__.CompactRoutingSetupLoadingSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1178,\n            columnNumber: 12\n        }, this);\n    }\n    if (error && !configDetails && !isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-3xl font-bold text-gray-900 mb-6\",\n                    children: \"Routing Setup Error\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1184,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card border-red-200 bg-red-50 p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-red-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1187,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-800\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/my-models\",\n                            className: \"mt-4 btn-primary inline-block\",\n                            children: \"Back to My Models\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1190,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1185,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1183,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#040716] text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleBackNavigation,\n                                    className: \"inline-flex items-center text-sm px-4 py-2 bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 hover:text-white rounded-lg border border-gray-700/50 hover:border-gray-600/50 transition-all duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1209,\n                                            columnNumber: 17\n                                        }, this),\n                                        getBackButtonText()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1205,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1204,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-h1 text-white\",\n                                        children: \"Advanced Routing Setup\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1214,\n                                        columnNumber: 15\n                                    }, this),\n                                    configDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-body-sm text-gray-400 mt-1\",\n                                        children: [\n                                            \"Configuration: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-orange-400 font-semibold\",\n                                                children: configDetails.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1219,\n                                                columnNumber: 34\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1218,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1213,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1203,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1202,\n                    columnNumber: 9\n                }, this),\n                error && !successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-900/20 border border-red-500/30 rounded-xl p-6 mb-8 max-w-4xl mx-auto backdrop-blur-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-red-500/20 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 text-red-400\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1232,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1231,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1230,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-red-300\",\n                                        children: \"Configuration Error\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1236,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-400 mt-1\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1237,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1235,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1229,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1228,\n                    columnNumber: 11\n                }, this),\n                successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-900/20 border border-green-500/30 rounded-xl p-6 mb-8 max-w-4xl mx-auto backdrop-blur-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 text-green-400\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M5 13l4 4L19 7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1248,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1247,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1246,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-green-300\",\n                                        children: \"Settings Saved\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1252,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-400 mt-1\",\n                                        children: successMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1253,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1251,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1245,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1244,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-xl p-6 sticky top-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold text-white mb-6\",\n                                            children: \"Routing Strategy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1265,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: ROUTING_STRATEGIES.map((strategy)=>{\n                                                const IconComponent = strategy.icon;\n                                                const isSelected = selectedStrategy === strategy.id;\n                                                // Define which strategies require advanced routing access\n                                                const advancedStrategies = [\n                                                    'intelligent_role',\n                                                    'complexity_round_robin',\n                                                    'auto_optimal',\n                                                    'cost_optimized',\n                                                    'ab_routing'\n                                                ];\n                                                const isAdvanced = advancedStrategies.includes(strategy.id);\n                                                const strategyButton = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setSelectedStrategy(strategy.id);\n                                                        // Reset/initialize params based on new strategy\n                                                        if (strategy.id === 'strict_fallback') {\n                                                            const existingFallbackParams = strategyParams.ordered_api_key_ids;\n                                                            if (existingFallbackParams && Array.isArray(existingFallbackParams)) {\n                                                                const reordered = existingFallbackParams.map((id)=>availableApiKeys.find((k)=>k.id === id)).filter(Boolean);\n                                                                const remaining = availableApiKeys.filter((k)=>!existingFallbackParams.includes(k.id));\n                                                                setOrderedFallbackKeys([\n                                                                    ...reordered,\n                                                                    ...remaining\n                                                                ]);\n                                                            } else {\n                                                                setOrderedFallbackKeys([\n                                                                    ...availableApiKeys\n                                                                ]);\n                                                            }\n                                                            setStrategyParams({\n                                                                ordered_api_key_ids: orderedFallbackKeys.map((k)=>k.id)\n                                                            });\n                                                        } else {\n                                                            setStrategyParams({});\n                                                            setOrderedFallbackKeys([\n                                                                ...availableApiKeys\n                                                            ]);\n                                                        }\n                                                        // Reset complexity assignment states if strategy changes\n                                                        setSelectedApiKeyForComplexity(null);\n                                                        setCurrentKeyComplexityLevels([]);\n                                                        setAssignmentsError(null);\n                                                        setAssignmentsSuccessMessage(null);\n                                                    },\n                                                    disabled: isLoading,\n                                                    className: \"w-full text-left p-4 rounded-xl border-2 transition-all duration-300 group \".concat(isSelected ? 'border-orange-500 bg-orange-500/10 shadow-lg transform scale-[1.02]' : 'border-gray-700/50 bg-gray-800/30 hover:border-orange-400/50 hover:bg-orange-500/5 hover:shadow-md'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 rounded-lg transition-colors duration-300 \".concat(isSelected ? 'bg-orange-500/20 text-orange-400' : 'bg-gray-700/50 text-gray-400 group-hover:bg-orange-500/10 group-hover:text-orange-400'),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                    className: \"w-6 h-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1314,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1309,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2 mb-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-sm transition-colors duration-300 \".concat(isSelected ? 'text-orange-300' : 'text-white'),\n                                                                                children: strategy.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1318,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BeakerIcon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"w-4 h-4 text-orange-400 animate-in fade-in duration-300\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1324,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1317,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs leading-relaxed transition-colors duration-300 \".concat(isSelected ? 'text-orange-400/80' : 'text-gray-400'),\n                                                                        children: strategy.shortDescription\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1327,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1316,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1308,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, strategy.id, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1276,\n                                                    columnNumber: 23\n                                                }, this);\n                                                // Wrap advanced strategies with TierGuard\n                                                if (isAdvanced) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_6__.TierGuard, {\n                                                        feature: \"advanced_routing\",\n                                                        customMessage: \"\".concat(strategy.name, \" is available starting with the Starter plan. Upgrade to access advanced routing strategies that optimize performance and cost.\"),\n                                                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"opacity-50 pointer-events-none\",\n                                                                    children: strategyButton\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1346,\n                                                                    columnNumber: 31\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gray-900/90 backdrop-blur-sm border border-orange-500/30 rounded-lg px-3 py-1\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs font-medium text-orange-400\",\n                                                                            children: \"Starter Plan Required\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1351,\n                                                                            columnNumber: 35\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1350,\n                                                                        columnNumber: 33\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1349,\n                                                                    columnNumber: 31\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1345,\n                                                            columnNumber: 29\n                                                        }, void 0),\n                                                        children: strategyButton\n                                                    }, strategy.id, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1340,\n                                                        columnNumber: 25\n                                                    }, this);\n                                                }\n                                                return strategyButton;\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1266,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1264,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1263,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSaveRoutingSettings,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-xl p-8 min-h-[600px]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-in fade-in slide-in-from-right-4 duration-500\",\n                                            children: renderConfigurationContent()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1373,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1371,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1370,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1369,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1261,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1260,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1200,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n        lineNumber: 1199,\n        columnNumber: 5\n    }, this);\n}\n_s(RoutingSetupConfigPage, \"Dz2Ft8UPkcwJ1nKHkSREVZZnayw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_4__.useRoutingSetupPrefetch\n    ];\n});\n_c2 = RoutingSetupConfigPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"DraggableApiKeyItem\");\n$RefreshReg$(_c1, \"SimpleApiKeyItem\");\n$RefreshReg$(_c2, \"RoutingSetupConfigPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcm91dGluZy1zZXR1cC9bY29uZmlnSWRdL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFb0U7QUFDSTtBQUMzQztBQWFRO0FBQ0c7QUFzQmtDO0FBQ2lEO0FBQ2xFO0FBY3pELE1BQU11QixxQkFNRDtJQUNIO1FBQ0VDLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxrQkFBa0I7UUFDbEJDLGFBQWE7UUFDYkMsTUFBTWxCLCtPQUFhQTtJQUNyQjtJQUNBO1FBQ0VjLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxrQkFBa0I7UUFDbEJDLGFBQWE7UUFDYkMsTUFBTWpCLCtPQUFRQTtJQUNoQjtJQUNBO1FBQ0VhLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxrQkFBa0I7UUFDbEJDLGFBQWE7UUFDYkMsTUFBTWhCLCtPQUFlQTtJQUN2QjtJQUNBO1FBQ0VZLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxrQkFBa0I7UUFDbEJDLGFBQWE7UUFDYkMsTUFBTWYsZ1BBQWNBO0lBQ3RCO0lBQ0E7UUFDRVcsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLGtCQUFrQjtRQUNsQkMsYUFBYTtRQUNiQyxNQUFNYixnUEFBa0JBO0lBQzFCO0lBQ0E7UUFDRVMsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLGtCQUFrQjtRQUNsQkMsYUFBYTtRQUNiQyxNQUFNWCxnUEFBVUE7SUFDbEI7Q0FDRDtBQUVELHVEQUF1RDtBQUN2RCxTQUFTWSxvQkFBb0IsS0FHNUI7UUFINEIsRUFBRUMsTUFBTSxFQUFFQyxLQUFLLEVBRzNDLEdBSDRCO0lBSTNCLHFCQUNFLDhEQUFDYix1REFBWTtRQUNYZSxPQUFPSDtRQUNQSSxXQUFVO1FBQ1ZDLFdBQVc7WUFDVEMsT0FBTztZQUNQQyxXQUFXO1lBQ1hDLFFBQVE7UUFDVjtRQUNBQyxjQUFjO1FBQ2RDLGNBQWNDO2tCQUVkLDRFQUFDQztZQUFJUixXQUFVOzs4QkFDYiw4REFBQ1E7b0JBQUlSLFdBQVU7O3NDQUNiLDhEQUFDUTs0QkFBSVIsV0FBVTtzQ0FDYiw0RUFBQ1M7Z0NBQUtULFdBQVU7MENBQXlDSCxRQUFROzs7Ozs7Ozs7OztzQ0FFbkUsOERBQUNXOzs4Q0FDQyw4REFBQ0M7b0NBQUtULFdBQVU7OENBQWtDSixPQUFPYyxLQUFLOzs7Ozs7OENBQzlELDhEQUFDQztvQ0FBRVgsV0FBVTs7d0NBQXlCSixPQUFPZ0IsUUFBUTt3Q0FBQzt3Q0FBSWhCLE9BQU9pQixtQkFBbUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBR3hGLDhEQUFDTDtvQkFBSVIsV0FBVTs4QkFDYiw0RUFBQ1E7d0JBQUlSLFdBQVU7a0NBQ2IsNEVBQUNsQixnUEFBU0E7NEJBQUNrQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNakM7S0FsQ1NMO0FBb0NULDhEQUE4RDtBQUM5RCxTQUFTbUIsaUJBQWlCLEtBS3pCO1FBTHlCLEVBQUVsQixNQUFNLEVBQUVDLEtBQUssRUFBRWtCLFFBQVEsRUFBRUMsVUFBVSxFQUs5RCxHQUx5QjtJQU14QixxQkFDRSw4REFBQ0M7UUFBR2pCLFdBQVU7a0JBQ1osNEVBQUNRO1lBQUlSLFdBQVU7OzhCQUNiLDhEQUFDUTtvQkFBSVIsV0FBVTs7c0NBQ2IsOERBQUNROzRCQUFJUixXQUFVO3NDQUNiLDRFQUFDUztnQ0FBS1QsV0FBVTswQ0FBeUNILFFBQVE7Ozs7Ozs7Ozs7O3NDQUVuRSw4REFBQ1c7OzhDQUNDLDhEQUFDQztvQ0FBS1QsV0FBVTs4Q0FBcUNKLE9BQU9jLEtBQUs7Ozs7Ozs4Q0FDakUsOERBQUNDO29DQUFFWCxXQUFVOzt3Q0FBeUJKLE9BQU9nQixRQUFRO3dDQUFDO3dDQUFJaEIsT0FBT2lCLG1CQUFtQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFHeEYsOERBQUNMO29CQUFJUixXQUFVOzt3QkFDWmUsMEJBQ0MsOERBQUNHOzRCQUNDQyxTQUFTSjs0QkFDVGYsV0FBVTs0QkFDVm9CLE9BQU07c0NBRU4sNEVBQUM5QyxnUEFBV0E7Z0NBQUMwQixXQUFVOzs7Ozs7Ozs7Ozt3QkFHMUJnQiw0QkFDQyw4REFBQ0U7NEJBQ0NDLFNBQVNIOzRCQUNUaEIsV0FBVTs0QkFDVm9CLE9BQU07c0NBRU4sNEVBQUM3QyxnUEFBYUE7Z0NBQUN5QixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT3ZDO01BekNTYztBQTJDTSxTQUFTTzs7SUFDdEIsTUFBTUMsU0FBU3JELDBEQUFTQTtJQUN4QixNQUFNc0QsU0FBU3JELDBEQUFTQTtJQUN4QixNQUFNc0QsZUFBZXJELGdFQUFlQTtJQUNwQyxNQUFNc0QsV0FBV0gsT0FBT0csUUFBUTtJQUVoQyxnQkFBZ0I7SUFDaEIsTUFBTSxFQUFFQyxhQUFhLEVBQUVDLFFBQVEsRUFBRSxHQUFHMUMsdUZBQXVCQTtJQUUzRCxxRUFBcUU7SUFDckUsTUFBTTJDLGFBQWE7UUFDakIsd0RBQXdEO1FBQ3hELE1BQU1DLFlBQVlMLGFBQWFNLEdBQUcsQ0FBQztRQUVuQyxJQUFJRCxjQUFjLGlCQUFpQjtZQUNqQyxPQUFPO1FBQ1Q7UUFFQSxJQUFJQSxjQUFjLGdCQUFnQjtZQUNoQyxPQUFPLGNBQXVCLE9BQVRKO1FBQ3ZCO1FBRUEsNERBQTREO1FBQzVELElBQUksSUFBNkIsRUFBRTtZQUNqQyxNQUFNTSxXQUFXQyxTQUFTRCxRQUFRO1lBQ2xDLE1BQU1FLGNBQWNDLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSTtZQUV4QyxJQUFJTCxZQUFZQSxTQUFTTSxRQUFRLENBQUNKLGNBQWM7Z0JBQzlDLElBQUk7b0JBQ0YsTUFBTUssY0FBYyxJQUFJQyxJQUFJUjtvQkFDNUIsTUFBTVMsZUFBZUYsWUFBWUcsUUFBUTtvQkFFekMsOERBQThEO29CQUM5RCxJQUFJRCxpQkFBaUIsa0JBQWtCO3dCQUNyQyxPQUFPO29CQUNUO29CQUVBLGlEQUFpRDtvQkFDakQsSUFBSUEsaUJBQWlCLGNBQXVCLE9BQVRmLFdBQVk7d0JBQzdDLE9BQU8sY0FBdUIsT0FBVEE7b0JBQ3ZCO29CQUVBLDRDQUE0QztvQkFDNUMsSUFBSWUsaUJBQWlCLGNBQWM7d0JBQ2pDLE9BQU8sY0FBdUIsT0FBVGY7b0JBQ3ZCO29CQUVBLHVFQUF1RTtvQkFDdkUsSUFBSWUsYUFBYUUsVUFBVSxDQUFDLGtCQUFrQixDQUFDRixhQUFhSCxRQUFRLENBQUMsbUJBQW1CO3dCQUN0RixPQUFPLGNBQXVCLE9BQVRaO29CQUN2QjtnQkFDRixFQUFFLE9BQU9rQixHQUFHO2dCQUNWLHFDQUFxQztnQkFDdkM7WUFDRjtRQUNGO1FBRUEsK0NBQStDO1FBQy9DLE9BQU8sY0FBdUIsT0FBVGxCO0lBQ3ZCO0lBRUEsTUFBTW1CLG9CQUFvQjtRQUN4Qix3REFBd0Q7UUFDeEQsTUFBTWYsWUFBWUwsYUFBYU0sR0FBRyxDQUFDO1FBRW5DLElBQUlELGNBQWMsaUJBQWlCO1lBQ2pDLE9BQU87UUFDVDtRQUVBLElBQUlBLGNBQWMsZ0JBQWdCO1lBQ2hDLE9BQU87UUFDVDtRQUVBLGlDQUFpQztRQUNqQyxJQUFJLElBQTZCLEVBQUU7WUFDakMsTUFBTUUsV0FBV0MsU0FBU0QsUUFBUTtZQUNsQyxNQUFNRSxjQUFjQyxPQUFPQyxRQUFRLENBQUNDLElBQUk7WUFFeEMsSUFBSUwsWUFBWUEsU0FBU00sUUFBUSxDQUFDSixjQUFjO2dCQUM5QyxJQUFJO29CQUNGLE1BQU1LLGNBQWMsSUFBSUMsSUFBSVI7b0JBQzVCLE1BQU1TLGVBQWVGLFlBQVlHLFFBQVE7b0JBRXpDLElBQUlELGlCQUFpQixrQkFBa0I7d0JBQ3JDLE9BQU87b0JBQ1Q7b0JBRUEsSUFBSUEsaUJBQWlCLGNBQXVCLE9BQVRmLGFBQWNlLGlCQUFpQixnQkFBZ0JBLGFBQWFFLFVBQVUsQ0FBQyxnQkFBZ0I7d0JBQ3hILE9BQU87b0JBQ1Q7Z0JBQ0YsRUFBRSxPQUFPQyxHQUFHO2dCQUNWLHFDQUFxQztnQkFDdkM7WUFDRjtRQUNGO1FBRUEsd0JBQXdCO1FBQ3hCLE9BQU87SUFDVDtJQUVBLE1BQU1FLHVCQUF1QjtRQUMzQixJQUFJLEtBQTZCLElBQUlYLE9BQU9ZLE9BQU8sQ0FBQ0MsTUFBTSxHQUFHLEdBQUc7WUFDOUQsNkNBQTZDO1lBQzdDLE1BQU1DLFVBQVVwQjtZQUNoQixNQUFNRyxXQUFXQyxTQUFTRCxRQUFRO1lBQ2xDLE1BQU1FLGNBQWNDLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSTtZQUV4Qyw4REFBOEQ7WUFDOUQsSUFBSUwsWUFBWUEsU0FBU00sUUFBUSxDQUFDSixjQUFjO2dCQUM5Q1YsT0FBTzBCLElBQUk7Z0JBQ1g7WUFDRjtRQUNGO1FBRUEsc0NBQXNDO1FBQ3RDMUIsT0FBTzJCLElBQUksQ0FBQ3RCO0lBQ2Q7SUFFQSxNQUFNLENBQUN1QixlQUFlQyxpQkFBaUIsR0FBR3JGLCtDQUFRQSxDQUF5QjtJQUMzRSxNQUFNLENBQUNzRixXQUFXQyxhQUFhLEdBQUd2RiwrQ0FBUUEsQ0FBVTtJQUNwRCxNQUFNLENBQUN3RixPQUFPQyxTQUFTLEdBQUd6RiwrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDMEYsZ0JBQWdCQyxrQkFBa0IsR0FBRzNGLCtDQUFRQSxDQUFnQjtJQUNwRSxNQUFNLENBQUM0Rix1QkFBdUJDLHlCQUF5QixHQUFHN0YsK0NBQVFBLENBQVU7SUFFNUUsTUFBTSxDQUFDOEYsa0JBQWtCQyxvQkFBb0IsR0FBRy9GLCtDQUFRQSxDQUFzQjtJQUM5RSxNQUFNLENBQUNnRyxnQkFBZ0JDLGtCQUFrQixHQUFHakcsK0NBQVFBLENBQU0sQ0FBQztJQUUzRCxNQUFNLENBQUNrRyxrQkFBa0JDLG9CQUFvQixHQUFHbkcsK0NBQVFBLENBQWtCLEVBQUU7SUFDNUUsTUFBTSxDQUFDb0csa0JBQWtCQyxvQkFBb0IsR0FBR3JHLCtDQUFRQSxDQUFVO0lBRWxFLGtEQUFrRDtJQUNsRCxNQUFNLENBQUNzRyxxQkFBcUJDLHVCQUF1QixHQUFHdkcsK0NBQVFBLENBQWtCLEVBQUU7SUFFbEYsNkNBQTZDO0lBQzdDLE1BQU0sQ0FBQ3dHLDZCQUE2QkMsK0JBQStCLEdBQUd6RywrQ0FBUUEsQ0FBZ0I7SUFDOUYsbUVBQW1FO0lBQ25FLE1BQU0sQ0FBQzBHLHVCQUF1QkMseUJBQXlCLEdBQUczRywrQ0FBUUEsQ0FBMkIsQ0FBQztJQUM5RixvRUFBb0U7SUFDcEUsTUFBTSxDQUFDNEcsNEJBQTRCQyw4QkFBOEIsR0FBRzdHLCtDQUFRQSxDQUFXLEVBQUU7SUFDekYsTUFBTSxDQUFDOEcsdUJBQXVCQyx5QkFBeUIsR0FBRy9HLCtDQUFRQSxDQUFVO0lBQzVFLE1BQU0sQ0FBQ2dILHFCQUFxQkMsdUJBQXVCLEdBQUdqSCwrQ0FBUUEsQ0FBVTtJQUN4RSxNQUFNLENBQUNrSCxrQkFBa0JDLG9CQUFvQixHQUFHbkgsK0NBQVFBLENBQWdCO0lBQ3hFLE1BQU0sQ0FBQ29ILDJCQUEyQkMsNkJBQTZCLEdBQUdySCwrQ0FBUUEsQ0FBZ0I7SUFFMUYsZ0RBQWdEO0lBQ2hELDhCQUE4QjtJQUM5Qiw4QkFBOEI7SUFDOUIsZ0NBQWdDO0lBQ2hDLHFEQUFxRDtJQUNyRCxPQUFPO0lBQ1AsS0FBSztJQUVMLG1EQUFtRDtJQUNuRCxvQ0FBb0M7SUFFcEMsa0NBQWtDO0lBQ2xDLHFGQUFxRjtJQUNyRixvRkFBb0Y7SUFFcEYsaUZBQWlGO0lBQ2pGLDhDQUE4QztJQUM5QyxzREFBc0Q7SUFDdEQsaUZBQWlGO0lBQ2pGLE1BQU07SUFDTixLQUFLO0lBRUwsTUFBTXNILHFCQUFxQnJILGtEQUFXQTtrRUFBQztZQUNyQyxJQUFJLENBQUN5RCxVQUFVO2dCQUNiK0IsU0FBUztnQkFDVEYsYUFBYTtnQkFDYjtZQUNGO1lBRUEsOEJBQThCO1lBQzlCLE1BQU1nQyxhQUFhNUQsY0FBY0Q7WUFDakMsSUFBSTZELGNBQWNBLFdBQVduQyxhQUFhLElBQUltQyxXQUFXQyxPQUFPLEVBQUU7b0JBV1hEO2dCQVZyREUsUUFBUUMsR0FBRyxDQUFDLG1EQUE0RCxPQUFUaEU7Z0JBRS9EMkIsaUJBQWlCa0MsV0FBV25DLGFBQWE7Z0JBQ3pDZSxvQkFBb0JvQixXQUFXQyxPQUFPO2dCQUV0QyxNQUFNRywwQkFBMEIsV0FBWUMsZUFBZSxJQUE0QjtnQkFDdkY3QixvQkFBb0I0QjtnQkFDcEIxQixrQkFBa0JzQixXQUFXTSxhQUFhLElBQUksQ0FBQztnQkFFL0Msc0RBQXNEO2dCQUN0RCxJQUFJRiw0QkFBNEIsdUJBQXFCSiw0QkFBQUEsV0FBV00sYUFBYSxjQUF4Qk4sZ0RBQUFBLDBCQUEwQk8sbUJBQW1CLEdBQUU7b0JBQ2xHLE1BQU1DLGFBQWFSLFdBQVdNLGFBQWEsQ0FBQ0MsbUJBQW1CO29CQUMvRCxNQUFNRSxnQkFBZ0JELFdBQVdFLEdBQUc7Z0dBQUMxRyxDQUFBQSxLQUFNZ0csV0FBV0MsT0FBTyxDQUFDVSxJQUFJO3dHQUFDQyxDQUFBQSxJQUFLQSxFQUFFNUcsRUFBRSxLQUFLQTs7K0ZBQUs2RyxNQUFNLENBQUNDO29CQUM3RixNQUFNQyxnQkFBZ0JmLFdBQVdDLE9BQU8sQ0FBQ1ksTUFBTTtnR0FBQ0QsQ0FBQUEsSUFBSyxDQUFDSixXQUFXekQsUUFBUSxDQUFDNkQsRUFBRTVHLEVBQUU7O29CQUM5RWdGLHVCQUF1QjsyQkFBSXlCOzJCQUFrQk07cUJBQWM7Z0JBQzdELE9BQU87b0JBQ0wvQix1QkFBdUI7MkJBQUlnQixXQUFXQyxPQUFPO3FCQUFDO2dCQUNoRDtnQkFFQWpDLGFBQWE7Z0JBQ2JjLG9CQUFvQjtnQkFDcEI7WUFDRjtZQUVBLGdEQUFnRDtZQUNoRCxJQUFJLENBQUN6QyxTQUFTRixXQUFXO2dCQUN2Qm1DLHlCQUF5QjtZQUMzQjtZQUVBTixhQUFhO1lBQ2JjLG9CQUFvQjtZQUNwQlosU0FBUztZQUNURSxrQkFBa0I7WUFFbEIsSUFBSTtnQkFDRix1QkFBdUI7Z0JBQ3ZCLE1BQU00QyxZQUFZLE1BQU1DLE1BQU0sdUJBQWdDLE9BQVQ5RTtnQkFDckQsSUFBSSxDQUFDNkUsVUFBVUUsRUFBRSxFQUFFO29CQUNqQixNQUFNQyxVQUFVLE1BQU1ILFVBQVVJLElBQUk7b0JBQ3BDLE1BQU0sSUFBSUMsTUFBTUYsUUFBUWxELEtBQUssSUFBSTtnQkFDbkM7Z0JBQ0EsTUFBTXFELGdCQUFpQyxNQUFNTixVQUFVSSxJQUFJO2dCQUMzRHRELGlCQUFpQndEO2dCQUNqQixNQUFNbEIsMEJBQTBCLGNBQWVtQixnQkFBZ0IsSUFBNEI7Z0JBQzNGL0Msb0JBQW9CNEI7Z0JBQ3BCLE1BQU1vQix3QkFBd0JGLGNBQWNHLHVCQUF1QixJQUFJLENBQUM7Z0JBQ3hFL0Msa0JBQWtCOEMsd0JBQXdCLHVCQUF1QjtnQkFFakUsd0NBQXdDO2dCQUN4QyxNQUFNRSxVQUFVLE1BQU1ULE1BQU0sOEJBQXVDLE9BQVQ5RTtnQkFDMUQsSUFBSSxDQUFDdUYsUUFBUVIsRUFBRSxFQUFFO29CQUNmLE1BQU1DLFVBQVUsTUFBTU8sUUFBUU4sSUFBSTtvQkFDbEMsTUFBTSxJQUFJQyxNQUFNRixRQUFRbEQsS0FBSyxJQUFJO2dCQUNuQztnQkFDQSxNQUFNMEQsT0FBd0IsTUFBTUQsUUFBUU4sSUFBSTtnQkFDaER4QyxvQkFBb0IrQztnQkFFcEIsd0VBQXdFO2dCQUN4RSxJQUFJdkIsNEJBQTRCLHFCQUFxQm9CLHNCQUFzQmpCLG1CQUFtQixFQUFFO29CQUM5RixNQUFNQyxhQUFhZ0Isc0JBQXNCakIsbUJBQW1CO29CQUM1RCxNQUFNRSxnQkFBZ0JELFdBQVdFLEdBQUc7Z0dBQUMxRyxDQUFBQSxLQUFNMkgsS0FBS2hCLElBQUk7d0dBQUNDLENBQUFBLElBQUtBLEVBQUU1RyxFQUFFLEtBQUtBOzsrRkFBSzZHLE1BQU0sQ0FBQ0M7b0JBQy9FLG9FQUFvRTtvQkFDcEUsTUFBTUMsZ0JBQWdCWSxLQUFLZCxNQUFNO2dHQUFDRCxDQUFBQSxJQUFLLENBQUNKLFdBQVd6RCxRQUFRLENBQUM2RCxFQUFFNUcsRUFBRTs7b0JBQ2hFZ0YsdUJBQXVCOzJCQUFJeUI7MkJBQWtCTTtxQkFBYztnQkFDN0QsT0FBTztvQkFDTC9CLHVCQUF1QjsyQkFBSTJDO3FCQUFLLEdBQUcsa0RBQWtEO2dCQUN2RjtZQUVGLEVBQUUsT0FBT0MsS0FBVTtnQkFDakIxRCxTQUFTLHVCQUFtQyxPQUFaMEQsSUFBSUMsT0FBTztnQkFDM0MvRCxpQkFBaUI7Z0JBQ2pCYyxvQkFBb0IsRUFBRTtZQUN4QixTQUFVO2dCQUNSWixhQUFhO2dCQUNiYyxvQkFBb0I7Z0JBQ3BCUix5QkFBeUI7WUFDM0I7UUFDRjtpRUFBRztRQUFDbkM7UUFBVUM7UUFBZUM7S0FBUztJQUV0QzdELGdEQUFTQTs0Q0FBQztZQUNSdUg7UUFDRjsyQ0FBRztRQUFDQTtLQUFtQjtJQUV2Qix3REFBd0Q7SUFDeEQsTUFBTStCLG1DQUFtQ3BKLGtEQUFXQTtnRkFBQyxPQUFPcUo7WUFDMUQsSUFBSSxDQUFDNUYsWUFBWSxDQUFDNEYsVUFBVTtZQUM1QnZDLHlCQUF5QjtZQUN6Qkksb0JBQW9CO1lBQ3BCRSw2QkFBNkI7WUFDN0IsSUFBSTtnQkFDRixNQUFNa0MsV0FBVyxNQUFNZixNQUFNLHVCQUF3Q2MsT0FBakI1RixVQUFTLFVBQWlCLE9BQVQ0RixVQUFTO2dCQUM5RSxJQUFJLENBQUNDLFNBQVNkLEVBQUUsRUFBRTtvQkFDaEIsTUFBTWUsWUFBWSxNQUFNRCxTQUFTWixJQUFJO29CQUNyQyxNQUFNLElBQUlDLE1BQU1ZLFVBQVVoRSxLQUFLLElBQUk7Z0JBQ3JDO2dCQUNBLE1BQU1pRSxnQkFBMEIsTUFBTUYsU0FBU1osSUFBSTtnQkFDbkRoQzs0RkFBeUIrQyxDQUFBQSxPQUFTOzRCQUFFLEdBQUdBLElBQUk7NEJBQUUsQ0FBQ0osU0FBUyxFQUFFRzt3QkFBYzs7Z0JBQ3ZFNUMsOEJBQThCNEM7WUFDaEMsRUFBRSxPQUFPTixLQUFVO2dCQUNqQmhDLG9CQUFvQix1Q0FBbUQsT0FBWmdDLElBQUlDLE9BQU87Z0JBQ3RFdkMsOEJBQThCLEVBQUUsR0FBRyxpQkFBaUI7WUFDdEQsU0FBVTtnQkFDUkUseUJBQXlCO1lBQzNCO1FBQ0Y7K0VBQUc7UUFBQ3JEO0tBQVM7SUFFYix1RUFBdUU7SUFDdkUzRCxnREFBU0E7NENBQUM7WUFDUixJQUFJeUcsNkJBQTZCO2dCQUMvQjZDLGlDQUFpQzdDO1lBQ25DLE9BQU87Z0JBQ0wscUNBQXFDO2dCQUNyQ0ssOEJBQThCLEVBQUU7Z0JBQ2hDTSxvQkFBb0I7WUFDdEI7UUFDRjsyQ0FBRztRQUFDWDtRQUE2QjZDO0tBQWlDO0lBRWxFLCtDQUErQztJQUMvQyxNQUFNTSw4QkFBOEIsQ0FBQ0MsT0FBZUM7UUFDbERoRCw4QkFBOEJpRCxDQUFBQTtZQUM1QixJQUFJRCxTQUFTO2dCQUNYLE9BQU87dUJBQUlDO29CQUFZRjtpQkFBTSxDQUFDRyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUQsSUFBSUM7WUFDbkQsT0FBTztnQkFDTCxPQUFPSCxXQUFXMUIsTUFBTSxDQUFDOEIsQ0FBQUEsSUFBS0EsTUFBTU47WUFDdEM7UUFDRjtJQUNGO0lBRUEsdURBQXVEO0lBQ3ZELE1BQU1PLGtDQUFrQ2xLLGtEQUFXQTsrRUFBQztZQUNsRCxJQUFJLENBQUN5RCxZQUFZLENBQUM4Qyw2QkFBNkI7Z0JBQzdDVyxvQkFBb0I7Z0JBQ3BCO1lBQ0Y7WUFDQUYsdUJBQXVCO1lBQ3ZCRSxvQkFBb0I7WUFDcEJFLDZCQUE2QjtZQUM3QixJQUFJO2dCQUNGLE1BQU1rQyxXQUFXLE1BQU1mLE1BQU0sdUJBQXdDaEMsT0FBakI5QyxVQUFTLFVBQW9DLE9BQTVCOEMsNkJBQTRCLDRCQUEwQjtvQkFDekg0RCxRQUFRO29CQUNSQyxTQUFTO3dCQUFFLGdCQUFnQjtvQkFBbUI7b0JBQzlDQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7d0JBQUVDLG1CQUFtQjdEO29CQUEyQjtnQkFDdkU7Z0JBQ0EsSUFBSSxDQUFDMkMsU0FBU2QsRUFBRSxFQUFFO29CQUNoQixNQUFNZSxZQUFZLE1BQU1ELFNBQVNaLElBQUk7b0JBQ3JDLE1BQU0sSUFBSUMsTUFBTVksVUFBVWhFLEtBQUssSUFBSTtnQkFDckM7Z0JBQ0EsTUFBTWtGLFNBQVMsTUFBTW5CLFNBQVNaLElBQUk7Z0JBQ2xDLG9EQUFvRDtnQkFDcERoQzsyRkFBeUIrQyxDQUFBQSxPQUFTOzRCQUFFLEdBQUdBLElBQUk7NEJBQUUsQ0FBQ2xELDRCQUE0QixFQUFFO21DQUFJSTs2QkFBMkI7d0JBQUM7O2dCQUM1R1MsNkJBQTZCcUQsT0FBT3RCLE9BQU8sSUFBSTtZQUNqRCxFQUFFLE9BQU9ELEtBQVU7Z0JBQ2pCaEMsb0JBQW9CLDZCQUF5QyxPQUFaZ0MsSUFBSUMsT0FBTztZQUM5RCxTQUFVO2dCQUNSbkMsdUJBQXVCO1lBQ3pCO1FBQ0Y7OEVBQUc7UUFBQ3ZEO1FBQVU4QztRQUE2Qkk7S0FBMkI7SUFFdEUsc0RBQXNEO0lBQ3RELE1BQU0rRCxvQkFBb0IsQ0FBQ0M7UUFDekJyRSx1QkFBdUJxRTtRQUN2QiwrQ0FBK0M7UUFDL0MzRSxrQkFBa0I7WUFBRTZCLHFCQUFxQjhDLFNBQVMzQyxHQUFHLENBQUNFLENBQUFBLElBQUtBLEVBQUU1RyxFQUFFO1FBQUU7SUFDbkU7SUFFQSxNQUFNc0osa0JBQWtCLENBQUMvSSxPQUFlZ0o7UUFDdEMsTUFBTUMsaUJBQWlCO2VBQUl6RTtTQUFvQjtRQUMvQyxNQUFNMEUsWUFBWUQsY0FBYyxDQUFDakosTUFBTTtRQUN2QyxJQUFJZ0osY0FBYyxRQUFRaEosUUFBUSxHQUFHO1lBQ25DaUosZUFBZUUsTUFBTSxDQUFDbkosT0FBTztZQUM3QmlKLGVBQWVFLE1BQU0sQ0FBQ25KLFFBQVEsR0FBRyxHQUFHa0o7UUFDdEMsT0FBTyxJQUFJRixjQUFjLFVBQVVoSixRQUFRaUosZUFBZS9GLE1BQU0sR0FBRyxHQUFHO1lBQ3BFK0YsZUFBZUUsTUFBTSxDQUFDbkosT0FBTztZQUM3QmlKLGVBQWVFLE1BQU0sQ0FBQ25KLFFBQVEsR0FBRyxHQUFHa0o7UUFDdEM7UUFDQXpFLHVCQUF1QndFO1FBQ3ZCLCtDQUErQztRQUMvQzlFLGtCQUFrQjtZQUFFNkIscUJBQXFCaUQsZUFBZTlDLEdBQUcsQ0FBQ0UsQ0FBQUEsSUFBS0EsRUFBRTVHLEVBQUU7UUFBRTtJQUN6RTtJQUVBLE1BQU0ySiw0QkFBNEIsT0FBT3RHO1FBQ3ZDQSxFQUFFdUcsY0FBYztRQUNoQixJQUFJLENBQUN6SCxZQUFZLENBQUMwQixlQUFlO1lBQy9CSyxTQUFTO1lBQ1Q7UUFDRjtRQUNBRixhQUFhO1FBQ2JFLFNBQVM7UUFDVEUsa0JBQWtCO1FBRWxCLElBQUl5RixlQUFlcEY7UUFDbkIsSUFBSUYscUJBQXFCLG1CQUFtQjtZQUMxQ3NGLGVBQWU7Z0JBQUV0RCxxQkFBcUJ4QixvQkFBb0IyQixHQUFHLENBQUNFLENBQUFBLElBQUtBLEVBQUU1RyxFQUFFO1lBQUU7UUFDM0U7UUFDQSx5RUFBeUU7UUFFekUsSUFBSTtZQUNGLE1BQU1nSSxXQUFXLE1BQU1mLE1BQU0sdUJBQWdDLE9BQVQ5RSxVQUFTLGFBQVc7Z0JBQ3RFMEcsUUFBUTtnQkFDUkMsU0FBUztvQkFBRSxnQkFBZ0I7Z0JBQW1CO2dCQUM5Q0MsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUNuQjFCLGtCQUFrQmhEO29CQUNsQmtELHlCQUF5Qm9DO2dCQUMzQjtZQUNGO1lBRUEsSUFBSSxDQUFDN0IsU0FBU2QsRUFBRSxFQUFFO2dCQUNoQixNQUFNZSxZQUFZLE1BQU1ELFNBQVNaLElBQUk7Z0JBQ3JDLE1BQU0sSUFBSUMsTUFBTVksVUFBVWhFLEtBQUssSUFBSTtZQUNyQztZQUNBLE1BQU1rRixTQUFTLE1BQU1uQixTQUFTWixJQUFJO1lBQ2xDaEQsa0JBQWtCK0UsT0FBT3RCLE9BQU8sSUFBSTtZQUNwQy9ELGlCQUFpQnFFLENBQUFBLE9BQVFBLE9BQU87b0JBQUUsR0FBR0EsSUFBSTtvQkFBRVosa0JBQWtCaEQ7b0JBQWtCa0QseUJBQXlCb0M7Z0JBQWEsSUFBSTtZQUN6SCx5RkFBeUY7WUFDekZuRixrQkFBa0JtRjtRQUVwQixFQUFFLE9BQU9qQyxLQUFVO1lBQ2pCMUQsU0FBUywwQkFBc0MsT0FBWjBELElBQUlDLE9BQU87UUFDaEQsU0FBVTtZQUNSN0QsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNOEYsK0JBQStCO1lBeUM2Rm5GO1FBeENoSSxJQUFJSixxQkFBcUIsMEJBQTBCLE9BQU87UUFFMUQscUJBQ0UsOERBQUNyRDtZQUFJUixXQUFVOzs4QkFDYiw4REFBQ3FKO29CQUFHckosV0FBVTs4QkFBMkM7Ozs7Ozs4QkFDekQsOERBQUNXO29CQUFFWCxXQUFVOzhCQUE2Qjs7Ozs7O2dCQUl6Q21FLGtDQUNDLDhEQUFDM0Q7b0JBQUlSLFdBQVU7O3NDQUNiLDhEQUFDUTs0QkFBSVIsV0FBVTs7Ozs7O3NDQUNmLDhEQUFDVzs0QkFBRVgsV0FBVTtzQ0FBNkI7Ozs7Ozs7Ozs7OztnQkFHN0MsQ0FBQ21FLG9CQUFvQkYsaUJBQWlCbEIsTUFBTSxLQUFLLG1CQUNoRCw4REFBQ3ZDO29CQUFJUixXQUFVOzhCQUNiLDRFQUFDVzt3QkFBRVgsV0FBVTtrQ0FBMEI7Ozs7Ozs7Ozs7O2dCQUkxQ2lFLGlCQUFpQmxCLE1BQU0sR0FBRyxtQkFDekIsOERBQUN2QztvQkFBSVIsV0FBVTs7c0NBQ2IsOERBQUNVOzRCQUFNNEksU0FBUTs0QkFBc0J0SixXQUFVO3NDQUErQzs7Ozs7O3NDQUM5Riw4REFBQ3VKOzRCQUNDakssSUFBRzs0QkFDSFMsT0FBT3dFLCtCQUErQjs0QkFDdENpRixVQUFVLENBQUM3RyxJQUFNNkIsK0JBQStCN0IsRUFBRThHLE1BQU0sQ0FBQzFKLEtBQUssSUFBSTs0QkFDbEVDLFdBQVU7OzhDQUVWLDhEQUFDMEo7b0NBQU8zSixPQUFNO29DQUFHNEosUUFBUTs4Q0FBQzs7Ozs7O2dDQUN6QjFGLGlCQUFpQitCLEdBQUcsQ0FBQzRELENBQUFBLG9CQUNwQiw4REFBQ0Y7d0NBQW9CM0osT0FBTzZKLElBQUl0SyxFQUFFOzs0Q0FBR3NLLElBQUlsSixLQUFLOzRDQUFDOzRDQUFHa0osSUFBSWhKLFFBQVE7NENBQUM7NENBQUlnSixJQUFJL0ksbUJBQW1COzRDQUFDOzt1Q0FBOUUrSSxJQUFJdEssRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBTTFCaUYsNkNBQ0MsOERBQUMvRDtvQkFBSVIsV0FBVTs7c0NBQ2IsOERBQUM2Sjs0QkFBRzdKLFdBQVU7O2dDQUF5Qzs4Q0FBOEIsOERBQUNTO29DQUFLVCxXQUFVOytDQUFtQmlFLHlCQUFBQSxpQkFBaUJnQyxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUU1RyxFQUFFLEtBQUtpRiwwQ0FBcENOLDZDQUFBQSx1QkFBa0V2RCxLQUFLOzs7Ozs7Ozs7Ozs7d0JBRTlMbUUsdUNBQ0MsOERBQUNyRTs0QkFBSVIsV0FBVTs7OENBQ2IsOERBQUNRO29DQUFJUixXQUFVOzs7Ozs7OENBQ2YsOERBQUNXO29DQUFFWCxXQUFVOzhDQUE2Qjs7Ozs7Ozs7Ozs7O3dCQUk3Q2lGLGtDQUNDLDhEQUFDekU7NEJBQUlSLFdBQVU7c0NBQ2IsNEVBQUNXO2dDQUFFWCxXQUFVOzBDQUF3QmlGOzs7Ozs7Ozs7Ozt3QkFJeENFLDJDQUNDLDhEQUFDM0U7NEJBQUlSLFdBQVU7c0NBQ2IsNEVBQUNXO2dDQUFFWCxXQUFVOzBDQUEwQm1GOzs7Ozs7Ozs7Ozt3QkFJMUMsQ0FBQ04sdUNBQ0EsOERBQUNyRTs0QkFBSVIsV0FBVTtzQ0FDWjtnQ0FBQztnQ0FBRztnQ0FBRztnQ0FBRztnQ0FBRzs2QkFBRSxDQUFDZ0csR0FBRyxDQUFDMkIsQ0FBQUEsc0JBQ25CLDhEQUFDakg7b0NBQWtCVixXQUFVOztzREFDM0IsOERBQUM4Sjs0Q0FDQ0MsTUFBSzs0Q0FDTG5DLFNBQVNqRCwyQkFBMkJ0QyxRQUFRLENBQUNzRjs0Q0FDN0M2QixVQUFVLENBQUM3RyxJQUFNK0UsNEJBQTRCQyxPQUFPaEYsRUFBRThHLE1BQU0sQ0FBQzdCLE9BQU87NENBQ3BFNUgsV0FBVTs7Ozs7O3NEQUVaLDhEQUFDUzs0Q0FBS1QsV0FBVTs7Z0RBQW9DO2dEQUFrQjJIOzs7Ozs7OzttQ0FQNURBOzs7Ozs7Ozs7O3NDQWFsQiw4REFBQ3pHOzRCQUNDQyxTQUFTK0c7NEJBQ1R5QixVQUFVNUUsdUJBQXVCRjs0QkFDakM3RSxXQUFVO3NDQUVUK0Usc0JBQXNCLDBCQUEwQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTTdEO0lBRUEsMERBQTBEO0lBQzFELE1BQU1pRiw2QkFBNkI7UUFDakMsTUFBTUMsdUJBQXVCNUssbUJBQW1CNEcsSUFBSSxDQUFDaUUsQ0FBQUEsSUFBS0EsRUFBRTVLLEVBQUUsS0FBS3VFO1FBRW5FLElBQUlBLHFCQUFxQixRQUFRO1lBQy9CLHFCQUNFLDhEQUFDckQ7Z0JBQUlSLFdBQVU7O2tDQUNiLDhEQUFDUTt3QkFBSVIsV0FBVTtrQ0FDYiw0RUFBQ3hCLCtPQUFhQTs0QkFBQ3dCLFdBQVU7Ozs7Ozs7Ozs7O2tDQUUzQiw4REFBQ3FKO3dCQUFHckosV0FBVTtrQ0FBcUM7Ozs7OztrQ0FDbkQsOERBQUNXO3dCQUFFWCxXQUFVO2tDQUNWaUssaUNBQUFBLDJDQUFBQSxxQkFBc0J4SyxXQUFXOzs7Ozs7a0NBRXBDLDhEQUFDZTt3QkFBSVIsV0FBVTtrQ0FDYiw0RUFBQ1E7NEJBQUlSLFdBQVU7OzhDQUNiLDhEQUFDcEIsZ1BBQWVBO29DQUFDb0IsV0FBVTs7Ozs7OzhDQUMzQiw4REFBQ1M7b0NBQUtULFdBQVU7OENBQTZCOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLakQsOERBQUNRO3dCQUFJUixXQUFVO2tDQUNiLDRFQUFDa0I7NEJBQ0M2SSxNQUFLOzRCQUNML0osV0FBVTs0QkFDVjJKLFVBQVV0RztzQ0FFVEEsMEJBQ0M7O2tEQUNFLDhEQUFDOEc7d0NBQUluSyxXQUFVO3dDQUE0Qm9LLE1BQUs7d0NBQU9DLFFBQU87d0NBQWVDLFNBQVE7a0RBQ25GLDRFQUFDQzs0Q0FBS0MsZUFBYzs0Q0FBUUMsZ0JBQWU7NENBQVFDLGFBQWE7NENBQUdDLEdBQUU7Ozs7Ozs7Ozs7O29DQUNqRTs7NkRBSVI7O2tEQUNFLDhEQUFDL0wsZ1BBQWVBO3dDQUFDb0IsV0FBVTs7Ozs7O29DQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OztRQVExRDtRQUVBLElBQUk2RCxxQkFBcUIsb0JBQW9CO1lBQzNDLHFCQUNFLDhEQUFDckQ7O2tDQUNDLDhEQUFDQTt3QkFBSVIsV0FBVTs7MENBQ2IsOERBQUNxSjtnQ0FBR3JKLFdBQVU7O2tEQUNaLDhEQUFDdkIsK09BQVFBO3dDQUFDdUIsV0FBVTs7Ozs7O29DQUFpQzs7Ozs7OzswQ0FHdkQsOERBQUNXO2dDQUFFWCxXQUFVOzBDQUNWaUssaUNBQUFBLDJDQUFBQSxxQkFBc0J4SyxXQUFXOzs7Ozs7Ozs7Ozs7a0NBSXRDLDhEQUFDZTt3QkFBSVIsV0FBVTs7MENBQ2IsOERBQUNRO2dDQUFJUixXQUFVOztrREFDYiw4REFBQzZKO3dDQUFHN0osV0FBVTtrREFBbUM7Ozs7OztrREFDakQsOERBQUNRO3dDQUFJUixXQUFVOzswREFDYiw4REFBQ1E7Z0RBQUlSLFdBQVU7O2tFQUNiLDhEQUFDUTt3REFBSVIsV0FBVTtrRUFDYiw0RUFBQ1M7NERBQUtULFdBQVU7c0VBQWtDOzs7Ozs7Ozs7OztrRUFFcEQsOERBQUNXO2tFQUFFOzs7Ozs7Ozs7Ozs7MERBRUwsOERBQUNIO2dEQUFJUixXQUFVOztrRUFDYiw4REFBQ1E7d0RBQUlSLFdBQVU7a0VBQ2IsNEVBQUNTOzREQUFLVCxXQUFVO3NFQUFrQzs7Ozs7Ozs7Ozs7a0VBRXBELDhEQUFDVztrRUFBRTs7Ozs7Ozs7Ozs7OzBEQUVMLDhEQUFDSDtnREFBSVIsV0FBVTs7a0VBQ2IsOERBQUNRO3dEQUFJUixXQUFVO2tFQUNiLDRFQUFDUzs0REFBS1QsV0FBVTtzRUFBa0M7Ozs7Ozs7Ozs7O2tFQUVwRCw4REFBQ1c7a0VBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLVCw4REFBQ0g7Z0NBQUlSLFdBQVU7MENBQ2IsNEVBQUNRO29DQUFJUixXQUFVOztzREFDYiw4REFBQ3BCLGdQQUFlQTs0Q0FBQ29CLFdBQVU7Ozs7OztzREFDM0IsOERBQUNROzs4REFDQyw4REFBQ3FKO29EQUFHN0osV0FBVTs4REFBb0M7Ozs7Ozs4REFDbEQsOERBQUNXO29EQUFFWCxXQUFVOzhEQUF5Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBUzlELDhEQUFDUTt3QkFBSVIsV0FBVTtrQ0FDYiw0RUFBQ2tCOzRCQUNDNkksTUFBSzs0QkFDTC9KLFdBQVU7NEJBQ1YySixVQUFVdEc7c0NBRVRBLDBCQUNDOztrREFDRSw4REFBQzhHO3dDQUFJbkssV0FBVTt3Q0FBNEJvSyxNQUFLO3dDQUFPQyxRQUFPO3dDQUFlQyxTQUFRO2tEQUNuRiw0RUFBQ0M7NENBQUtDLGVBQWM7NENBQVFDLGdCQUFlOzRDQUFRQyxhQUFhOzRDQUFHQyxHQUFFOzs7Ozs7Ozs7OztvQ0FDakU7OzZEQUlSOztrREFDRSw4REFBQy9MLGdQQUFlQTt3Q0FBQ29CLFdBQVU7Ozs7OztvQ0FBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7UUFRMUQ7UUFFQSxJQUFJNkQscUJBQXFCLG1CQUFtQjtZQUMxQyxxQkFDRSw4REFBQ3JEOztrQ0FDQyw4REFBQ0E7d0JBQUlSLFdBQVU7OzBDQUNiLDhEQUFDcUo7Z0NBQUdySixXQUFVOztrREFDWiw4REFBQ3JCLGdQQUFjQTt3Q0FBQ3FCLFdBQVU7Ozs7OztvQ0FBaUM7Ozs7Ozs7MENBRzdELDhEQUFDVztnQ0FBRVgsV0FBVTswQ0FDVmlLLGlDQUFBQSwyQ0FBQUEscUJBQXNCeEssV0FBVzs7Ozs7Ozs7Ozs7O29CQUlyQzBFLGtDQUNDLDhEQUFDM0Q7d0JBQUlSLFdBQVU7OzBDQUNiLDhEQUFDUTtnQ0FBSVIsV0FBVTs7Ozs7OzBDQUNmLDhEQUFDVztnQ0FBRVgsV0FBVTswQ0FBcUI7Ozs7Ozs7Ozs7OztvQkFJckMsQ0FBQ21FLG9CQUFvQkYsaUJBQWlCbEIsTUFBTSxLQUFLLG1CQUNoRCw4REFBQ3ZDO3dCQUFJUixXQUFVOzswQ0FDYiw4REFBQ1E7Z0NBQUlSLFdBQVU7MENBQ2IsNEVBQUNtSztvQ0FBSW5LLFdBQVU7b0NBQTBCb0ssTUFBSztvQ0FBT0MsUUFBTztvQ0FBZUMsU0FBUTs4Q0FDakYsNEVBQUNDO3dDQUFLQyxlQUFjO3dDQUFRQyxnQkFBZTt3Q0FBUUMsYUFBYTt3Q0FBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OzswQ0FHekUsOERBQUNkO2dDQUFHN0osV0FBVTswQ0FBNkM7Ozs7OzswQ0FDM0QsOERBQUNXO2dDQUFFWCxXQUFVOzBDQUFrQzs7Ozs7Ozs7Ozs7O29CQU1sRCxDQUFDbUUsb0JBQW9CRixpQkFBaUJsQixNQUFNLEdBQUcsbUJBQzlDLDhEQUFDdkM7d0JBQUlSLFdBQVU7OzBDQUNiLDhEQUFDUTtnQ0FBSVIsV0FBVTswQ0FDYiw0RUFBQ1E7b0NBQUlSLFdBQVU7O3NEQUNiLDhEQUFDUTs0Q0FBSVIsV0FBVTtzREFDYiw0RUFBQ21LO2dEQUFJbkssV0FBVTtnREFBd0JvSyxNQUFLO2dEQUFPQyxRQUFPO2dEQUFlQyxTQUFROzBEQUMvRSw0RUFBQ0M7b0RBQUtDLGVBQWM7b0RBQVFDLGdCQUFlO29EQUFRQyxhQUFhO29EQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O3NEQUd6RSw4REFBQ2hLOzRDQUFFWCxXQUFVO3NEQUF3Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBT3pELDhEQUFDaEIsd0RBQWE7Z0NBQ1o2TCxNQUFLO2dDQUNMQyxRQUFRekc7Z0NBQ1IwRyxXQUFXckM7Z0NBQ1gxSSxXQUFVOzBDQUVUcUUsb0JBQW9CMkIsR0FBRyxDQUFDLENBQUNwRyxRQUFRQyxzQkFDaEMsOERBQUNGO3dDQUVDQyxRQUFRQTt3Q0FDUkMsT0FBT0E7dUNBRkZELE9BQU9OLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBVXhCLDhEQUFDa0I7d0JBQUlSLFdBQVU7a0NBQ2IsNEVBQUNrQjs0QkFDQzZJLE1BQUs7NEJBQ0wvSixXQUFVOzRCQUNWMkosVUFBVXRHLGFBQWFZLGlCQUFpQmxCLE1BQU0sS0FBSztzQ0FFbERNLDBCQUNDOztrREFDRSw4REFBQzhHO3dDQUFJbkssV0FBVTt3Q0FBNEJvSyxNQUFLO3dDQUFPQyxRQUFPO3dDQUFlQyxTQUFRO2tEQUNuRiw0RUFBQ0M7NENBQUtDLGVBQWM7NENBQVFDLGdCQUFlOzRDQUFRQyxhQUFhOzRDQUFHQyxHQUFFOzs7Ozs7Ozs7OztvQ0FDakU7OzZEQUlSOztrREFDRSw4REFBQy9MLGdQQUFlQTt3Q0FBQ29CLFdBQVU7Ozs7OztvQ0FBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7UUFRMUQ7UUFFQSxJQUFJNkQscUJBQXFCLDBCQUEwQjtZQUNqRCxxQkFDRSw4REFBQ3JEOztrQ0FDQyw4REFBQ0E7d0JBQUlSLFdBQVU7OzBDQUNiLDhEQUFDcUo7Z0NBQUdySixXQUFVOztrREFDWiw4REFBQ3RCLCtPQUFlQTt3Q0FBQ3NCLFdBQVU7Ozs7OztvQ0FBaUM7Ozs7Ozs7MENBRzlELDhEQUFDVztnQ0FBRVgsV0FBVTswQ0FDVmlLLGlDQUFBQSwyQ0FBQUEscUJBQXNCeEssV0FBVzs7Ozs7Ozs7Ozs7O29CQUlyQzJKO2tDQUdELDhEQUFDNUk7d0JBQUlSLFdBQVU7a0NBQ2IsNEVBQUNrQjs0QkFDQzZJLE1BQUs7NEJBQ0wvSixXQUFVOzRCQUNWMkosVUFBVXRHO3NDQUVUQSwwQkFDQzs7a0RBQ0UsOERBQUM4Rzt3Q0FBSW5LLFdBQVU7d0NBQTRCb0ssTUFBSzt3Q0FBT0MsUUFBTzt3Q0FBZUMsU0FBUTtrREFDbkYsNEVBQUNDOzRDQUFLQyxlQUFjOzRDQUFRQyxnQkFBZTs0Q0FBUUMsYUFBYTs0Q0FBR0MsR0FBRTs7Ozs7Ozs7Ozs7b0NBQ2pFOzs2REFJUjs7a0RBQ0UsOERBQUMvTCxnUEFBZUE7d0NBQUNvQixXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O1FBUTFEO1FBRUEsSUFBSTZELHFCQUFxQixrQkFBa0I7WUFDekMscUJBQ0UsOERBQUNyRDs7a0NBQ0MsOERBQUNBO3dCQUFJUixXQUFVOzswQ0FDYiw4REFBQ3FKO2dDQUFHckosV0FBVTs7a0RBQ1osOERBQUNuQixnUEFBa0JBO3dDQUFDbUIsV0FBVTs7Ozs7O29DQUFpQzs7Ozs7OzswQ0FHakUsOERBQUNXO2dDQUFFWCxXQUFVOzBDQUNWaUssaUNBQUFBLDJDQUFBQSxxQkFBc0J4SyxXQUFXOzs7Ozs7Ozs7Ozs7a0NBSXRDLDhEQUFDZTt3QkFBSVIsV0FBVTs7MENBQ2IsOERBQUNRO2dDQUFJUixXQUFVOztrREFDYiw4REFBQzZKO3dDQUFHN0osV0FBVTtrREFBbUM7Ozs7OztrREFDakQsOERBQUNRO3dDQUFJUixXQUFVOzswREFDYiw4REFBQ1E7Z0RBQUlSLFdBQVU7O2tFQUNiLDhEQUFDUTt3REFBSVIsV0FBVTtrRUFDYiw0RUFBQ1M7NERBQUtULFdBQVU7c0VBQWtDOzs7Ozs7Ozs7OztrRUFFcEQsOERBQUNXOzswRUFBRSw4REFBQ3FLOzBFQUFPOzs7Ozs7NERBQTBCOzs7Ozs7Ozs7Ozs7OzBEQUV2Qyw4REFBQ3hLO2dEQUFJUixXQUFVOztrRUFDYiw4REFBQ1E7d0RBQUlSLFdBQVU7a0VBQ2IsNEVBQUNTOzREQUFLVCxXQUFVO3NFQUFrQzs7Ozs7Ozs7Ozs7a0VBRXBELDhEQUFDVzs7NERBQUU7MEVBQWlDLDhEQUFDcUs7MEVBQU87Ozs7Ozs0REFBcUI7MEVBQW9CLDhEQUFDQTswRUFBTzs7Ozs7OzREQUFzQjs7Ozs7Ozs7Ozs7OzswREFFckgsOERBQUN4SztnREFBSVIsV0FBVTs7a0VBQ2IsOERBQUNRO3dEQUFJUixXQUFVO2tFQUNiLDRFQUFDUzs0REFBS1QsV0FBVTtzRUFBa0M7Ozs7Ozs7Ozs7O2tFQUVwRCw4REFBQ1c7a0VBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLVCw4REFBQ0g7Z0NBQUlSLFdBQVU7O2tEQUNiLDhEQUFDUTt3Q0FBSVIsV0FBVTs7MERBQ2IsOERBQUNRO2dEQUFJUixXQUFVOztrRUFDYiw4REFBQ1E7d0RBQUlSLFdBQVU7Ozs7OztrRUFDZiw4REFBQ2lMO3dEQUFHakwsV0FBVTtrRUFBdUM7Ozs7Ozs7Ozs7OzswREFFdkQsOERBQUNXO2dEQUFFWCxXQUFVOztvREFBeUM7a0VBQ2MsOERBQUNnTDtrRUFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUk5RSw4REFBQ3hLO3dDQUFJUixXQUFVOzswREFDYiw4REFBQ1E7Z0RBQUlSLFdBQVU7O2tFQUNiLDhEQUFDUTt3REFBSVIsV0FBVTs7Ozs7O2tFQUNmLDhEQUFDaUw7d0RBQUdqTCxXQUFVO2tFQUF3Qzs7Ozs7Ozs7Ozs7OzBEQUV4RCw4REFBQ1c7Z0RBQUVYLFdBQVU7O29EQUEwQztrRUFDUCw4REFBQ2dMO2tFQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSTFELDhEQUFDeEs7d0NBQUlSLFdBQVU7OzBEQUNiLDhEQUFDUTtnREFBSVIsV0FBVTs7a0VBQ2IsOERBQUNRO3dEQUFJUixXQUFVOzs7Ozs7a0VBQ2YsOERBQUNpTDt3REFBR2pMLFdBQVU7a0VBQXdDOzs7Ozs7Ozs7Ozs7MERBRXhELDhEQUFDVztnREFBRVgsV0FBVTs7b0RBQTBDO2tFQUNkLDhEQUFDZ0w7a0VBQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLckQsOERBQUN4SztnQ0FBSVIsV0FBVTswQ0FDYiw0RUFBQ1E7b0NBQUlSLFdBQVU7O3NEQUNiLDhEQUFDbkIsZ1BBQWtCQTs0Q0FBQ21CLFdBQVU7Ozs7OztzREFDOUIsOERBQUNROzs4REFDQyw4REFBQ3FKO29EQUFHN0osV0FBVTs4REFBcUM7Ozs7Ozs4REFDbkQsOERBQUNXO29EQUFFWCxXQUFVOzhEQUEwQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTzdELDhEQUFDUTtnQ0FBSVIsV0FBVTswQ0FDYiw0RUFBQ1E7b0NBQUlSLFdBQVU7O3NEQUNiLDhEQUFDcEIsZ1BBQWVBOzRDQUFDb0IsV0FBVTs7Ozs7O3NEQUMzQiw4REFBQ1E7OzhEQUNDLDhEQUFDcUo7b0RBQUc3SixXQUFVOzhEQUFvQzs7Ozs7OzhEQUNsRCw4REFBQ1c7b0RBQUVYLFdBQVU7OERBQXlDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FTOUQsOERBQUNRO3dCQUFJUixXQUFVO2tDQUNiLDRFQUFDa0I7NEJBQ0M2SSxNQUFLOzRCQUNML0osV0FBVTs0QkFDVjJKLFVBQVV0RztzQ0FFVEEsMEJBQ0M7O2tEQUNFLDhEQUFDOEc7d0NBQUluSyxXQUFVO3dDQUE0Qm9LLE1BQUs7d0NBQU9DLFFBQU87d0NBQWVDLFNBQVE7a0RBQ25GLDRFQUFDQzs0Q0FBS0MsZUFBYzs0Q0FBUUMsZ0JBQWU7NENBQVFDLGFBQWE7NENBQUdDLEdBQUU7Ozs7Ozs7Ozs7O29DQUNqRTs7NkRBSVI7O2tEQUNFLDhEQUFDL0wsZ1BBQWVBO3dDQUFDb0IsV0FBVTs7Ozs7O29DQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OztRQVExRDtRQUVBLElBQUk2RCxxQkFBcUIsY0FBYztZQUNyQyxxQkFDRSw4REFBQ3JEOztrQ0FDQyw4REFBQ0E7d0JBQUlSLFdBQVU7OzBDQUNiLDhEQUFDcUo7Z0NBQUdySixXQUFVOztrREFDWiw4REFBQ2pCLGdQQUFVQTt3Q0FBQ2lCLFdBQVU7Ozs7OztvQ0FBaUM7Ozs7Ozs7MENBR3pELDhEQUFDVztnQ0FBRVgsV0FBVTswQ0FDVmlLLGlDQUFBQSwyQ0FBQUEscUJBQXNCeEssV0FBVzs7Ozs7Ozs7Ozs7O2tDQUl0Qyw4REFBQ2U7d0JBQUlSLFdBQVU7OzBDQUNiLDhEQUFDUTtnQ0FBSVIsV0FBVTs7a0RBQ2IsOERBQUM2Sjt3Q0FBRzdKLFdBQVU7a0RBQW1DOzs7Ozs7a0RBQ2pELDhEQUFDUTt3Q0FBSVIsV0FBVTs7MERBQ2IsOERBQUNRO2dEQUFJUixXQUFVOztrRUFDYiw4REFBQ1E7d0RBQUlSLFdBQVU7a0VBQ2IsNEVBQUNTOzREQUFLVCxXQUFVO3NFQUFrQzs7Ozs7Ozs7Ozs7a0VBRXBELDhEQUFDVzs7MEVBQUUsOERBQUNxSzswRUFBTzs7Ozs7OzREQUEyQjs7Ozs7Ozs7Ozs7OzswREFFeEMsOERBQUN4SztnREFBSVIsV0FBVTs7a0VBQ2IsOERBQUNRO3dEQUFJUixXQUFVO2tFQUNiLDRFQUFDUzs0REFBS1QsV0FBVTtzRUFBa0M7Ozs7Ozs7Ozs7O2tFQUVwRCw4REFBQ1c7OzBFQUFFLDhEQUFDcUs7MEVBQU87Ozs7Ozs0REFBd0I7Ozs7Ozs7Ozs7Ozs7MERBRXJDLDhEQUFDeEs7Z0RBQUlSLFdBQVU7O2tFQUNiLDhEQUFDUTt3REFBSVIsV0FBVTtrRUFDYiw0RUFBQ1M7NERBQUtULFdBQVU7c0VBQWtDOzs7Ozs7Ozs7OztrRUFFcEQsOERBQUNXOzswRUFBRSw4REFBQ3FLOzBFQUFPOzs7Ozs7NERBQTZCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUs5Qyw4REFBQ3hLO2dDQUFJUixXQUFVOztrREFDYiw4REFBQ1E7d0NBQUlSLFdBQVU7OzBEQUNiLDhEQUFDUTtnREFBSVIsV0FBVTs7a0VBQ2IsOERBQUNRO3dEQUFJUixXQUFVOzs7Ozs7a0VBQ2YsOERBQUNpTDt3REFBR2pMLFdBQVU7a0VBQXVDOzs7Ozs7Ozs7Ozs7MERBRXZELDhEQUFDVztnREFBRVgsV0FBVTswREFBeUM7Ozs7Ozs7Ozs7OztrREFLeEQsOERBQUNRO3dDQUFJUixXQUFVOzswREFDYiw4REFBQ1E7Z0RBQUlSLFdBQVU7O2tFQUNiLDhEQUFDUTt3REFBSVIsV0FBVTs7Ozs7O2tFQUNmLDhEQUFDaUw7d0RBQUdqTCxXQUFVO2tFQUF3Qzs7Ozs7Ozs7Ozs7OzBEQUV4RCw4REFBQ1c7Z0RBQUVYLFdBQVU7MERBQTBDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTTNELDhEQUFDUTtnQ0FBSVIsV0FBVTswQ0FDYiw0RUFBQ1E7b0NBQUlSLFdBQVU7O3NEQUNiLDhEQUFDakIsZ1BBQVVBOzRDQUFDaUIsV0FBVTs7Ozs7O3NEQUN0Qiw4REFBQ1E7OzhEQUNDLDhEQUFDcUo7b0RBQUc3SixXQUFVOzhEQUFxQzs7Ozs7OzhEQUNuRCw4REFBQ1c7b0RBQUVYLFdBQVU7OERBQTBDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FPN0QsOERBQUNRO2dDQUFJUixXQUFVOzBDQUNiLDRFQUFDUTtvQ0FBSVIsV0FBVTs7c0RBQ2IsOERBQUNwQixnUEFBZUE7NENBQUNvQixXQUFVOzs7Ozs7c0RBQzNCLDhEQUFDUTs7OERBQ0MsOERBQUNxSjtvREFBRzdKLFdBQVU7OERBQW9DOzs7Ozs7OERBQ2xELDhEQUFDVztvREFBRVgsV0FBVTs4REFBeUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVM5RCw4REFBQ1E7d0JBQUlSLFdBQVU7a0NBQ2IsNEVBQUNrQjs0QkFDQzZJLE1BQUs7NEJBQ0wvSixXQUFVOzRCQUNWMkosVUFBVXRHO3NDQUVUQSwwQkFDQzs7a0RBQ0UsOERBQUM4Rzt3Q0FBSW5LLFdBQVU7d0NBQTRCb0ssTUFBSzt3Q0FBT0MsUUFBTzt3Q0FBZUMsU0FBUTtrREFDbkYsNEVBQUNDOzRDQUFLQyxlQUFjOzRDQUFRQyxnQkFBZTs0Q0FBUUMsYUFBYTs0Q0FBR0MsR0FBRTs7Ozs7Ozs7Ozs7b0NBQ2pFOzs2REFJUjs7a0RBQ0UsOERBQUMvTCxnUEFBZUE7d0NBQUNvQixXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O1FBUTFEO1FBRUEsT0FBTztJQUNUO0lBRUEsNENBQTRDO0lBQzVDLElBQUkyRCx5QkFBeUIsQ0FBQ2hDLFNBQVNGLFdBQVc7UUFDaEQscUJBQU8sOERBQUN2QywrRUFBMkJBOzs7OztJQUNyQztJQUVBLElBQUltRSxhQUFhLENBQUNGLGVBQWU7UUFDL0IscUJBQU8sOERBQUNoRSx1R0FBa0NBOzs7OztJQUM1QztJQUVBLElBQUlvRSxTQUFTLENBQUNKLGlCQUFpQixDQUFDRSxXQUFXO1FBQ3pDLHFCQUNFLDhEQUFDN0M7WUFBSVIsV0FBVTs7OEJBQ2IsOERBQUNrTDtvQkFBR2xMLFdBQVU7OEJBQXdDOzs7Ozs7OEJBQ3RELDhEQUFDUTtvQkFBSVIsV0FBVTs7c0NBQ2IsOERBQUNROzRCQUFJUixXQUFVOzs4Q0FDYiw4REFBQ1E7b0NBQUlSLFdBQVU7Ozs7Ozs4Q0FDZiw4REFBQ1c7b0NBQUVYLFdBQVU7OENBQWdCdUQ7Ozs7Ozs7Ozs7OztzQ0FFL0IsOERBQUNuRixrREFBSUE7NEJBQUMrTSxNQUFLOzRCQUFhbkwsV0FBVTtzQ0FBZ0M7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU0xRTtJQUVBLHFCQUNFLDhEQUFDUTtRQUFJUixXQUFVO2tCQUNiLDRFQUFDUTtZQUFJUixXQUFVOzs4QkFFYiw4REFBQ1E7b0JBQUlSLFdBQVU7OEJBQ2IsNEVBQUNRO3dCQUFJUixXQUFVOzswQ0FDYiw4REFBQ1E7Z0NBQUlSLFdBQVU7MENBQ2IsNEVBQUNrQjtvQ0FDQ0MsU0FBUzBCO29DQUNUN0MsV0FBVTs7c0RBRVYsOERBQUMzQixnUEFBYUE7NENBQUMyQixXQUFVOzs7Ozs7d0NBQ3hCNEM7Ozs7Ozs7Ozs7OzswQ0FHTCw4REFBQ3BDO2dDQUFJUixXQUFVOztrREFDYiw4REFBQ2tMO3dDQUFHbEwsV0FBVTtrREFBcUI7Ozs7OztvQ0FHbENtRCwrQkFDQyw4REFBQ3hDO3dDQUFFWCxXQUFVOzs0Q0FBa0M7MERBQzlCLDhEQUFDUztnREFBS1QsV0FBVTswREFBaUNtRCxjQUFjNUQsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBUTFGZ0UsU0FBUyxDQUFDRSxnQ0FDViw4REFBQ2pEO29CQUFJUixXQUFVOzhCQUNiLDRFQUFDUTt3QkFBSVIsV0FBVTs7MENBQ2IsOERBQUNRO2dDQUFJUixXQUFVOzBDQUNiLDRFQUFDbUs7b0NBQUluSyxXQUFVO29DQUF1Qm9LLE1BQUs7b0NBQU9DLFFBQU87b0NBQWVDLFNBQVE7OENBQzlFLDRFQUFDQzt3Q0FBS0MsZUFBYzt3Q0FBUUMsZ0JBQWU7d0NBQVFDLGFBQWE7d0NBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7MENBR3pFLDhEQUFDbks7O2tEQUNDLDhEQUFDNkk7d0NBQUdySixXQUFVO2tEQUE2Qjs7Ozs7O2tEQUMzQyw4REFBQ1c7d0NBQUVYLFdBQVU7a0RBQXFCdUQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dCQU16Q0UsZ0NBQ0MsOERBQUNqRDtvQkFBSVIsV0FBVTs4QkFDYiw0RUFBQ1E7d0JBQUlSLFdBQVU7OzBDQUNiLDhEQUFDUTtnQ0FBSVIsV0FBVTswQ0FDYiw0RUFBQ21LO29DQUFJbkssV0FBVTtvQ0FBeUJvSyxNQUFLO29DQUFPQyxRQUFPO29DQUFlQyxTQUFROzhDQUNoRiw0RUFBQ0M7d0NBQUtDLGVBQWM7d0NBQVFDLGdCQUFlO3dDQUFRQyxhQUFhO3dDQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzBDQUd6RSw4REFBQ25LOztrREFDQyw4REFBQzZJO3dDQUFHckosV0FBVTtrREFBK0I7Ozs7OztrREFDN0MsOERBQUNXO3dDQUFFWCxXQUFVO2tEQUF1QnlEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFPNUMsOERBQUNqRDtvQkFBSVIsV0FBVTs4QkFDYiw0RUFBQ1E7d0JBQUlSLFdBQVU7OzBDQUViLDhEQUFDUTtnQ0FBSVIsV0FBVTswQ0FDYiw0RUFBQ1E7b0NBQUlSLFdBQVU7O3NEQUNiLDhEQUFDb0w7NENBQUdwTCxXQUFVO3NEQUFvQzs7Ozs7O3NEQUNsRCw4REFBQ1E7NENBQUlSLFdBQVU7c0RBQ1pYLG1CQUFtQjJHLEdBQUcsQ0FBQyxDQUFDcUY7Z0RBQ3ZCLE1BQU1DLGdCQUFnQkQsU0FBUzNMLElBQUk7Z0RBQ25DLE1BQU02TCxhQUFhMUgscUJBQXFCd0gsU0FBUy9MLEVBQUU7Z0RBRW5ELDBEQUEwRDtnREFDMUQsTUFBTWtNLHFCQUFxQjtvREFBQztvREFBb0I7b0RBQTBCO29EQUFnQjtvREFBa0I7aURBQWE7Z0RBQ3pILE1BQU1DLGFBQWFELG1CQUFtQm5KLFFBQVEsQ0FBQ2dKLFNBQVMvTCxFQUFFO2dEQUUxRCxNQUFNb00sK0JBQ0osOERBQUN4SztvREFFQ0MsU0FBUzt3REFDUDJDLG9CQUFvQnVILFNBQVMvTCxFQUFFO3dEQUMvQixnREFBZ0Q7d0RBQ2hELElBQUkrTCxTQUFTL0wsRUFBRSxLQUFLLG1CQUFtQjs0REFDckMsTUFBTXFNLHlCQUF5QjVILGVBQWU4QixtQkFBbUI7NERBQ2pFLElBQUk4RiwwQkFBMEJDLE1BQU1DLE9BQU8sQ0FBQ0YseUJBQXlCO2dFQUNuRSxNQUFNRyxZQUFZSCx1QkFBdUIzRixHQUFHLENBQUMxRyxDQUFBQSxLQUFNMkUsaUJBQWlCZ0MsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFNUcsRUFBRSxLQUFLQSxLQUFLNkcsTUFBTSxDQUFDQztnRUFDbkcsTUFBTTJGLFlBQVk5SCxpQkFBaUJrQyxNQUFNLENBQUNELENBQUFBLElBQUssQ0FBQ3lGLHVCQUF1QnRKLFFBQVEsQ0FBQzZELEVBQUU1RyxFQUFFO2dFQUNwRmdGLHVCQUF1Qjt1RUFBSXdIO3VFQUFjQztpRUFBVTs0REFDckQsT0FBTztnRUFDTHpILHVCQUF1Qjt1RUFBSUw7aUVBQWlCOzREQUM5Qzs0REFDQUQsa0JBQWtCO2dFQUFFNkIscUJBQXFCeEIsb0JBQW9CMkIsR0FBRyxDQUFDRSxDQUFBQSxJQUFLQSxFQUFFNUcsRUFBRTs0REFBRTt3REFDOUUsT0FBTzs0REFDTDBFLGtCQUFrQixDQUFDOzREQUNuQk0sdUJBQXVCO21FQUFJTDs2REFBaUI7d0RBQzlDO3dEQUNBLHlEQUF5RDt3REFDekRPLCtCQUErQjt3REFDL0JJLDhCQUE4QixFQUFFO3dEQUNoQ00sb0JBQW9CO3dEQUNwQkUsNkJBQTZCO29EQUMvQjtvREFDQXVFLFVBQVV0RztvREFDVnJELFdBQVcsOEVBSVYsT0FIQ3VMLGFBQ0ksd0VBQ0E7OERBR04sNEVBQUMvSzt3REFBSVIsV0FBVTs7MEVBQ2IsOERBQUNRO2dFQUFJUixXQUFXLGlEQUlmLE9BSEN1TCxhQUNJLHFDQUNBOzBFQUVKLDRFQUFDRDtvRUFBY3RMLFdBQVU7Ozs7Ozs7Ozs7OzBFQUUzQiw4REFBQ1E7Z0VBQUlSLFdBQVU7O2tGQUNiLDhEQUFDUTt3RUFBSVIsV0FBVTs7MEZBQ2IsOERBQUNxSjtnRkFBR3JKLFdBQVcsd0RBRWQsT0FEQ3VMLGFBQWEsb0JBQW9COzBGQUVoQ0YsU0FBUzlMLElBQUk7Ozs7Ozs0RUFFZmdNLDRCQUNDLDhEQUFDM00sZ1BBQWVBO2dGQUFDb0IsV0FBVTs7Ozs7Ozs7Ozs7O2tGQUcvQiw4REFBQ1c7d0VBQUVYLFdBQVcsMERBRWIsT0FEQ3VMLGFBQWEsdUJBQXVCO2tGQUVuQ0YsU0FBUzdMLGdCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O21EQXJEM0I2TCxTQUFTL0wsRUFBRTs7Ozs7Z0RBNERwQiwwQ0FBMEM7Z0RBQzFDLElBQUltTSxZQUFZO29EQUNkLHFCQUNFLDhEQUFDck0sa0VBQVNBO3dEQUVSNE0sU0FBUTt3REFDUkMsZUFBZSxHQUFpQixPQUFkWixTQUFTOUwsSUFBSSxFQUFDO3dEQUNoQzJNLHdCQUNFLDhEQUFDMUw7NERBQUlSLFdBQVU7OzhFQUNiLDhEQUFDUTtvRUFBSVIsV0FBVTs4RUFDWjBMOzs7Ozs7OEVBRUgsOERBQUNsTDtvRUFBSVIsV0FBVTs4RUFDYiw0RUFBQ1E7d0VBQUlSLFdBQVU7a0ZBQ2IsNEVBQUNTOzRFQUFLVCxXQUFVO3NGQUFzQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFNN0QwTDt1REFoQklMLFNBQVMvTCxFQUFFOzs7OztnREFtQnRCO2dEQUVBLE9BQU9vTTs0Q0FDVDs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTU4sOERBQUNsTDtnQ0FBSVIsV0FBVTswQ0FDYiw0RUFBQ21NO29DQUFLQyxVQUFVbkQ7OENBQ2QsNEVBQUN6STt3Q0FBSVIsV0FBVTtrREFFYiw0RUFBQ1E7NENBQUlSLFdBQVU7c0RBQ1pnSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVVyQjtHQTNxQ3dCM0k7O1FBQ1BwRCxzREFBU0E7UUFDVEMsc0RBQVNBO1FBQ0hDLDREQUFlQTtRQUlBYyxtRkFBdUJBOzs7TUFQckNvQyIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXGFwcFxccm91dGluZy1zZXR1cFxcW2NvbmZpZ0lkXVxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlLCBGb3JtRXZlbnQsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUGFyYW1zLCB1c2VSb3V0ZXIsIHVzZVNlYXJjaFBhcmFtcyB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuaW1wb3J0IHtcbiAgQXJyb3dMZWZ0SWNvbixcbiAgQXJyb3dVcEljb24sXG4gIEFycm93RG93bkljb24sXG4gIENvZzZUb290aEljb24sXG4gIEJvbHRJY29uLFxuICBDaXJjbGVTdGFja0ljb24sXG4gIExpc3RCdWxsZXRJY29uLFxuICBDaGVja0NpcmNsZUljb24sXG4gIEN1cnJlbmN5RG9sbGFySWNvbixcbiAgQmFyczNJY29uLFxuICBCZWFrZXJJY29uXG59IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSc7XG5pbXBvcnQgeyBSZW9yZGVyIH0gZnJvbSAnZnJhbWVyLW1vdGlvbic7XG4vLyBUZW1wb3JhcmlseSBkaXNhYmxlIERuRCBLaXQgdG8gYXZvaWQgcG90ZW50aWFsIGNvbmZsaWN0c1xuLy8gaW1wb3J0IHtcbi8vICAgRG5kQ29udGV4dCxcbi8vICAgY2xvc2VzdENlbnRlcixcbi8vICAgS2V5Ym9hcmRTZW5zb3IsXG4vLyAgIFBvaW50ZXJTZW5zb3IsXG4vLyAgIHVzZVNlbnNvcixcbi8vICAgdXNlU2Vuc29ycyxcbi8vICAgRHJhZ0VuZEV2ZW50LFxuLy8gfSBmcm9tICdAZG5kLWtpdC9jb3JlJztcbi8vIGltcG9ydCB7XG4vLyAgIGFycmF5TW92ZSxcbi8vICAgU29ydGFibGVDb250ZXh0LFxuLy8gICBzb3J0YWJsZUtleWJvYXJkQ29vcmRpbmF0ZXMsXG4vLyAgIHZlcnRpY2FsTGlzdFNvcnRpbmdTdHJhdGVneSxcbi8vIH0gZnJvbSAnQGRuZC1raXQvc29ydGFibGUnO1xuLy8gaW1wb3J0IHtcbi8vICAgdXNlU29ydGFibGUsXG4vLyB9IGZyb20gJ0BkbmQta2l0L3NvcnRhYmxlJztcbi8vIGltcG9ydCB7IENTUyB9IGZyb20gJ0BkbmQta2l0L3V0aWxpdGllcyc7XG5pbXBvcnQgeyB0eXBlIERpc3BsYXlBcGlLZXkgfSBmcm9tICdAL3R5cGVzL2FwaUtleXMnOyAvLyBBc3N1bWluZyB0aGlzIHR5cGUgaXMgZGVmaW5lZFxuaW1wb3J0IHsgdXNlUm91dGluZ1NldHVwUHJlZmV0Y2ggfSBmcm9tICdAL2hvb2tzL3VzZVJvdXRpbmdTZXR1cFByZWZldGNoJztcbmltcG9ydCBSb3V0aW5nU2V0dXBMb2FkaW5nU2tlbGV0b24sIHsgQ29tcGFjdFJvdXRpbmdTZXR1cExvYWRpbmdTa2VsZXRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy9Sb3V0aW5nU2V0dXBMb2FkaW5nU2tlbGV0b24nO1xuaW1wb3J0IHsgVGllckd1YXJkIH0gZnJvbSAnQC9jb21wb25lbnRzL1RpZXJFbmZvcmNlbWVudCc7XG5cbmludGVyZmFjZSBDdXN0b21BcGlDb25maWcge1xuICBpZDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIGNyZWF0ZWRfYXQ6IHN0cmluZztcbiAgdXBkYXRlZF9hdDogc3RyaW5nO1xuICB1c2VyX2lkOiBzdHJpbmc7XG4gIHJvdXRpbmdfc3RyYXRlZ3k6IHN0cmluZyB8IG51bGw7XG4gIHJvdXRpbmdfc3RyYXRlZ3lfcGFyYW1zOiBhbnkgfCBudWxsOyBcbn1cblxudHlwZSBSb3V0aW5nU3RyYXRlZ3lUeXBlID0gJ25vbmUnIHwgJ2ludGVsbGlnZW50X3JvbGUnIHwgJ2NvbXBsZXhpdHlfcm91bmRfcm9iaW4nIHwgJ3N0cmljdF9mYWxsYmFjaycgfCAnY29zdF9vcHRpbWl6ZWQnIHwgJ2FiX3JvdXRpbmcnO1xuXG5jb25zdCBST1VUSU5HX1NUUkFURUdJRVM6IEFycmF5PHtcbiAgaWQ6IFJvdXRpbmdTdHJhdGVneVR5cGU7XG4gIG5hbWU6IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgaWNvbjogUmVhY3QuQ29tcG9uZW50VHlwZTx7IGNsYXNzTmFtZT86IHN0cmluZyB9PjtcbiAgc2hvcnREZXNjcmlwdGlvbjogc3RyaW5nO1xufT4gPSBbXG4gIHtcbiAgICBpZDogJ25vbmUnLFxuICAgIG5hbWU6ICdEZWZhdWx0IEJlaGF2aW9yJyxcbiAgICBzaG9ydERlc2NyaXB0aW9uOiAnQXV0b21hdGljIGxvYWQgYmFsYW5jaW5nJyxcbiAgICBkZXNjcmlwdGlvbjogXCJSb3VLZXkgd2lsbCBhdXRvbWF0aWNhbGx5IGxvYWQgYmFsYW5jZSBhY3Jvc3MgYWxsIGtleXMgYXNzaWduZWQgdG8gdGhpcyBjb25maWd1cmF0aW9uIHdpdGggaW50cmEtcmVxdWVzdCByZXRyaWVzLiBObyBleHRyYSBzZXR1cCBuZWVkZWQuXCIsXG4gICAgaWNvbjogQ29nNlRvb3RoSWNvblxuICB9LFxuICB7XG4gICAgaWQ6ICdpbnRlbGxpZ2VudF9yb2xlJyxcbiAgICBuYW1lOiAnSW50ZWxsaWdlbnQgUm9sZSBSb3V0aW5nJyxcbiAgICBzaG9ydERlc2NyaXB0aW9uOiAnQUktcG93ZXJlZCByb2xlIGNsYXNzaWZpY2F0aW9uJyxcbiAgICBkZXNjcmlwdGlvbjogXCJSb3VLZXkgdXNlcyBhbiBMTE0gdG8gY2xhc3NpZnkgdGhlIHVzZXIncyBwcm9tcHQgYW5kIHJvdXRlcyB0byBhIGtleSBhc3NvY2lhdGVkIHdpdGggdGhhdCByb2xlLiBJZiBubyBtYXRjaCwgdXNlcyB0aGUgJ0RlZmF1bHQgR2VuZXJhbCBDaGF0IE1vZGVsJy5cIixcbiAgICBpY29uOiBCb2x0SWNvblxuICB9LFxuICB7XG4gICAgaWQ6ICdjb21wbGV4aXR5X3JvdW5kX3JvYmluJyxcbiAgICBuYW1lOiAnQ29tcGxleGl0eS1CYXNlZCBSb3VuZC1Sb2JpbicsXG4gICAgc2hvcnREZXNjcmlwdGlvbjogJ1JvdXRlIGJ5IHByb21wdCBjb21wbGV4aXR5JyxcbiAgICBkZXNjcmlwdGlvbjogJ1JvdUtleSBjbGFzc2lmaWVzIHByb21wdCBjb21wbGV4aXR5ICgxLTUpIGFuZCByb3VuZC1yb2JpbnMgYW1vbmcgYWN0aXZlIGtleXMgYXNzaWduZWQgdG8gdGhhdCBjb21wbGV4aXR5LiBTZWFyY2hlcyBwcm94aW1hbCBsZXZlbHMgaWYgbm8gZXhhY3QgbWF0Y2guJyxcbiAgICBpY29uOiBDaXJjbGVTdGFja0ljb25cbiAgfSxcbiAge1xuICAgIGlkOiAnc3RyaWN0X2ZhbGxiYWNrJyxcbiAgICBuYW1lOiAnU3RyaWN0IEZhbGxiYWNrJyxcbiAgICBzaG9ydERlc2NyaXB0aW9uOiAnT3JkZXJlZCBmYWlsb3ZlciBzZXF1ZW5jZScsXG4gICAgZGVzY3JpcHRpb246ICdEZWZpbmUgYW4gb3JkZXJlZCBsaXN0IG9mIEFQSSBrZXlzLiBSb3VLZXkgd2lsbCB0cnkgdGhlbSBpbiBzZXF1ZW5jZSB1bnRpbCBvbmUgc3VjY2VlZHMuJyxcbiAgICBpY29uOiBMaXN0QnVsbGV0SWNvblxuICB9LFxuICB7XG4gICAgaWQ6ICdjb3N0X29wdGltaXplZCcsXG4gICAgbmFtZTogJ0Nvc3QtT3B0aW1pemVkIFJvdXRpbmcnLFxuICAgIHNob3J0RGVzY3JpcHRpb246ICdTbWFydCBjb3N0LXBlcmZvcm1hbmNlIGJhbGFuY2UnLFxuICAgIGRlc2NyaXB0aW9uOiAnUm91S2V5IGludGVsbGlnZW50bHkgYmFsYW5jZXMgY29zdCBhbmQgcGVyZm9ybWFuY2UgYnkgcm91dGluZyBzaW1wbGUgdGFza3MgdG8gY2hlYXBlciBtb2RlbHMgYW5kIGNvbXBsZXggdGFza3MgdG8gcHJlbWl1bSBtb2RlbHMuIE1heGltaXplcyBzYXZpbmdzIHdoaWxlIGVuc3VyaW5nIHF1YWxpdHkuJyxcbiAgICBpY29uOiBDdXJyZW5jeURvbGxhckljb25cbiAgfSxcbiAge1xuICAgIGlkOiAnYWJfcm91dGluZycsXG4gICAgbmFtZTogJ0EvQiBSb3V0aW5nJyxcbiAgICBzaG9ydERlc2NyaXB0aW9uOiAnQ29udGludW91cyBtb2RlbCBvcHRpbWl6YXRpb24nLFxuICAgIGRlc2NyaXB0aW9uOiAnUm91S2V5IGNvbnRpbnVvdXNseSB0ZXN0cyBkaWZmZXJlbnQgbW9kZWxzIHdpdGggMTUlIG9mIHJlcXVlc3RzIHRvIGZpbmQgdGhlIGJlc3QgcGVyZm9ybWluZyBtb2RlbHMgZm9yIHlvdXIgc3BlY2lmaWMgdXNlIGNhc2VzLiBBdXRvbWF0aWNhbGx5IG9wdGltaXplcyByb3V0aW5nIGJhc2VkIG9uIHF1YWxpdHkgYW5kIGNvc3QgbWV0cmljcy4nLFxuICAgIGljb246IEJlYWtlckljb25cbiAgfSxcbl07XG5cbi8vIERyYWdnYWJsZSBBUEkgS2V5IEl0ZW0gQ29tcG9uZW50IGZvciBTdHJpY3QgRmFsbGJhY2tcbmZ1bmN0aW9uIERyYWdnYWJsZUFwaUtleUl0ZW0oeyBhcGlLZXksIGluZGV4IH06IHtcbiAgYXBpS2V5OiBEaXNwbGF5QXBpS2V5O1xuICBpbmRleDogbnVtYmVyO1xufSkge1xuICByZXR1cm4gKFxuICAgIDxSZW9yZGVyLkl0ZW1cbiAgICAgIHZhbHVlPXthcGlLZXl9XG4gICAgICBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMC81MCBib3JkZXIgYm9yZGVyLWdyYXktNzAwLzUwIHJvdW5kZWQtbGcgcC00IHNoYWRvdy1zbSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgaG92ZXI6c2hhZG93LW1kIGhvdmVyOmJvcmRlci1ncmF5LTYwMC81MCBjdXJzb3ItZ3JhYiBhY3RpdmU6Y3Vyc29yLWdyYWJiaW5nIGJhY2tkcm9wLWJsdXItc21cIlxuICAgICAgd2hpbGVEcmFnPXt7XG4gICAgICAgIHNjYWxlOiAxLjAyLFxuICAgICAgICBib3hTaGFkb3c6IFwiMCAxMHB4IDI1cHggcmdiYSgwLCAwLCAwLCAwLjMpXCIsXG4gICAgICAgIHpJbmRleDogMTAwMCxcbiAgICAgIH19XG4gICAgICBkcmFnTGlzdGVuZXI9e3RydWV9XG4gICAgICBkcmFnQ29udHJvbHM9e3VuZGVmaW5lZH1cbiAgICA+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1vcmFuZ2UtNTAwLzIwIGJvcmRlciBib3JkZXItb3JhbmdlLTUwMC8zMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1vcmFuZ2UtNDAwXCI+e2luZGV4ICsgMX08L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZVwiPnthcGlLZXkubGFiZWx9PC9zcGFuPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwXCI+e2FwaUtleS5wcm92aWRlcn0gLSB7YXBpS2V5LnByZWRlZmluZWRfbW9kZWxfaWR9PC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMiB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS0zMDAgdHJhbnNpdGlvbi1jb2xvcnMgY3Vyc29yLWdyYWIgYWN0aXZlOmN1cnNvci1ncmFiYmluZ1wiPlxuICAgICAgICAgICAgPEJhcnMzSWNvbiBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L1Jlb3JkZXIuSXRlbT5cbiAgKTtcbn1cblxuLy8gU2ltcGxpZmllZCBBUEkgS2V5IEl0ZW0gQ29tcG9uZW50IChmb3Igbm9uLWRyYWdnYWJsZSBsaXN0cylcbmZ1bmN0aW9uIFNpbXBsZUFwaUtleUl0ZW0oeyBhcGlLZXksIGluZGV4LCBvbk1vdmVVcCwgb25Nb3ZlRG93biB9OiB7XG4gIGFwaUtleTogRGlzcGxheUFwaUtleTtcbiAgaW5kZXg6IG51bWJlcjtcbiAgb25Nb3ZlVXA/OiAoKSA9PiB2b2lkO1xuICBvbk1vdmVEb3duPzogKCkgPT4gdm9pZDtcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8bGkgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkLWxnIHAtNCBzaGFkb3ctc20gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGhvdmVyOnNoYWRvdy1tZCBob3Zlcjpib3JkZXItZ3JheS0zMDBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLW9yYW5nZS01MCBib3JkZXIgYm9yZGVyLW9yYW5nZS0yMDAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtb3JhbmdlLTYwMFwiPntpbmRleCArIDF9PC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj57YXBpS2V5LmxhYmVsfTwvc3Bhbj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTYwMFwiPnthcGlLZXkucHJvdmlkZXJ9IC0ge2FwaUtleS5wcmVkZWZpbmVkX21vZGVsX2lkfTwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xXCI+XG4gICAgICAgICAge29uTW92ZVVwICYmIChcbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17b25Nb3ZlVXB9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMSB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtb3JhbmdlLTYwMCBob3ZlcjpiZy1vcmFuZ2UtNTAgcm91bmRlZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgICAgICB0aXRsZT1cIk1vdmUgdXBcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8QXJyb3dVcEljb24gY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICApfVxuICAgICAgICAgIHtvbk1vdmVEb3duICYmIChcbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17b25Nb3ZlRG93bn1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0xIHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1vcmFuZ2UtNjAwIGhvdmVyOmJnLW9yYW5nZS01MCByb3VuZGVkIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgIHRpdGxlPVwiTW92ZSBkb3duXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPEFycm93RG93bkljb24gY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvbGk+XG4gICk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvdXRpbmdTZXR1cENvbmZpZ1BhZ2UoKSB7XG4gIGNvbnN0IHBhcmFtcyA9IHVzZVBhcmFtcygpO1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3Qgc2VhcmNoUGFyYW1zID0gdXNlU2VhcmNoUGFyYW1zKCk7XG4gIGNvbnN0IGNvbmZpZ0lkID0gcGFyYW1zLmNvbmZpZ0lkIGFzIHN0cmluZztcblxuICAvLyBQcmVmZXRjaCBob29rXG4gIGNvbnN0IHsgZ2V0Q2FjaGVkRGF0YSwgaXNDYWNoZWQgfSA9IHVzZVJvdXRpbmdTZXR1cFByZWZldGNoKCk7XG5cbiAgLy8gU21hcnQgYmFjayBuYXZpZ2F0aW9uIHVzaW5nIHNlYXJjaCBwYXJhbXMgYW5kIGZhbGxiYWNrIHRvIHJlZmVycmVyXG4gIGNvbnN0IGdldEJhY2tVcmwgPSAoKSA9PiB7XG4gICAgLy8gRmlyc3QsIGNoZWNrIGlmIHdlIGhhdmUgYSAnZnJvbScgcGFyYW1ldGVyIGluIHRoZSBVUkxcbiAgICBjb25zdCBmcm9tUGFyYW0gPSBzZWFyY2hQYXJhbXMuZ2V0KCdmcm9tJyk7XG5cbiAgICBpZiAoZnJvbVBhcmFtID09PSAncm91dGluZy1zZXR1cCcpIHtcbiAgICAgIHJldHVybiAnL3JvdXRpbmctc2V0dXAnO1xuICAgIH1cblxuICAgIGlmIChmcm9tUGFyYW0gPT09ICdtb2RlbC1jb25maWcnKSB7XG4gICAgICByZXR1cm4gYC9teS1tb2RlbHMvJHtjb25maWdJZH1gO1xuICAgIH1cblxuICAgIC8vIEZhbGxiYWNrIHRvIHJlZmVycmVyIGRldGVjdGlvbiBmb3IgYmFja3dhcmQgY29tcGF0aWJpbGl0eVxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgY29uc3QgcmVmZXJyZXIgPSBkb2N1bWVudC5yZWZlcnJlcjtcbiAgICAgIGNvbnN0IGN1cnJlbnRIb3N0ID0gd2luZG93LmxvY2F0aW9uLmhvc3Q7XG5cbiAgICAgIGlmIChyZWZlcnJlciAmJiByZWZlcnJlci5pbmNsdWRlcyhjdXJyZW50SG9zdCkpIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zdCByZWZlcnJlclVybCA9IG5ldyBVUkwocmVmZXJyZXIpO1xuICAgICAgICAgIGNvbnN0IHJlZmVycmVyUGF0aCA9IHJlZmVycmVyVXJsLnBhdGhuYW1lO1xuXG4gICAgICAgICAgLy8gSWYgdGhleSBjYW1lIGZyb20gdGhlIG1haW4gcm91dGluZyBzZXR1cCBwYWdlIChleGFjdCBtYXRjaClcbiAgICAgICAgICBpZiAocmVmZXJyZXJQYXRoID09PSAnL3JvdXRpbmctc2V0dXAnKSB7XG4gICAgICAgICAgICByZXR1cm4gJy9yb3V0aW5nLXNldHVwJztcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAvLyBJZiB0aGV5IGNhbWUgZnJvbSB0aGUgbW9kZWwgY29uZmlndXJhdGlvbiBwYWdlXG4gICAgICAgICAgaWYgKHJlZmVycmVyUGF0aCA9PT0gYC9teS1tb2RlbHMvJHtjb25maWdJZH1gKSB7XG4gICAgICAgICAgICByZXR1cm4gYC9teS1tb2RlbHMvJHtjb25maWdJZH1gO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIElmIHRoZXkgY2FtZSBmcm9tIHRoZSBteS1tb2RlbHMgbGlzdCBwYWdlXG4gICAgICAgICAgaWYgKHJlZmVycmVyUGF0aCA9PT0gJy9teS1tb2RlbHMnKSB7XG4gICAgICAgICAgICByZXR1cm4gYC9teS1tb2RlbHMvJHtjb25maWdJZH1gO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIElmIHRoZXkgY2FtZSBmcm9tIGFueSBvdGhlciBteS1tb2RlbHMgcGFnZSwgZ28gdG8gdGhpcyBjb25maWcncyBwYWdlXG4gICAgICAgICAgaWYgKHJlZmVycmVyUGF0aC5zdGFydHNXaXRoKCcvbXktbW9kZWxzLycpICYmICFyZWZlcnJlclBhdGguaW5jbHVkZXMoJy9yb3V0aW5nLXNldHVwJykpIHtcbiAgICAgICAgICAgIHJldHVybiBgL215LW1vZGVscy8ke2NvbmZpZ0lkfWA7XG4gICAgICAgICAgfVxuICAgICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgICAgLy8gU2lsZW50bHkgaGFuZGxlIFVSTCBwYXJzaW5nIGVycm9yc1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gRGVmYXVsdCBmYWxsYmFjayB0byBtb2RlbCBjb25maWd1cmF0aW9uIHBhZ2VcbiAgICByZXR1cm4gYC9teS1tb2RlbHMvJHtjb25maWdJZH1gO1xuICB9O1xuXG4gIGNvbnN0IGdldEJhY2tCdXR0b25UZXh0ID0gKCkgPT4ge1xuICAgIC8vIEZpcnN0LCBjaGVjayBpZiB3ZSBoYXZlIGEgJ2Zyb20nIHBhcmFtZXRlciBpbiB0aGUgVVJMXG4gICAgY29uc3QgZnJvbVBhcmFtID0gc2VhcmNoUGFyYW1zLmdldCgnZnJvbScpO1xuXG4gICAgaWYgKGZyb21QYXJhbSA9PT0gJ3JvdXRpbmctc2V0dXAnKSB7XG4gICAgICByZXR1cm4gJ0JhY2sgdG8gUm91dGluZyBTZXR1cCc7XG4gICAgfVxuXG4gICAgaWYgKGZyb21QYXJhbSA9PT0gJ21vZGVsLWNvbmZpZycpIHtcbiAgICAgIHJldHVybiAnQmFjayB0byBDb25maWd1cmF0aW9uJztcbiAgICB9XG5cbiAgICAvLyBGYWxsYmFjayB0byByZWZlcnJlciBkZXRlY3Rpb25cbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIGNvbnN0IHJlZmVycmVyID0gZG9jdW1lbnQucmVmZXJyZXI7XG4gICAgICBjb25zdCBjdXJyZW50SG9zdCA9IHdpbmRvdy5sb2NhdGlvbi5ob3N0O1xuXG4gICAgICBpZiAocmVmZXJyZXIgJiYgcmVmZXJyZXIuaW5jbHVkZXMoY3VycmVudEhvc3QpKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3QgcmVmZXJyZXJVcmwgPSBuZXcgVVJMKHJlZmVycmVyKTtcbiAgICAgICAgICBjb25zdCByZWZlcnJlclBhdGggPSByZWZlcnJlclVybC5wYXRobmFtZTtcblxuICAgICAgICAgIGlmIChyZWZlcnJlclBhdGggPT09ICcvcm91dGluZy1zZXR1cCcpIHtcbiAgICAgICAgICAgIHJldHVybiAnQmFjayB0byBSb3V0aW5nIFNldHVwJztcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBpZiAocmVmZXJyZXJQYXRoID09PSBgL215LW1vZGVscy8ke2NvbmZpZ0lkfWAgfHwgcmVmZXJyZXJQYXRoID09PSAnL215LW1vZGVscycgfHwgcmVmZXJyZXJQYXRoLnN0YXJ0c1dpdGgoJy9teS1tb2RlbHMvJykpIHtcbiAgICAgICAgICAgIHJldHVybiAnQmFjayB0byBDb25maWd1cmF0aW9uJztcbiAgICAgICAgICB9XG4gICAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgICAvLyBTaWxlbnRseSBoYW5kbGUgVVJMIHBhcnNpbmcgZXJyb3JzXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBEZWZhdWx0IGZhbGxiYWNrIHRleHRcbiAgICByZXR1cm4gJ0JhY2sgdG8gQ29uZmlndXJhdGlvbic7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQmFja05hdmlnYXRpb24gPSAoKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHdpbmRvdy5oaXN0b3J5Lmxlbmd0aCA+IDEpIHtcbiAgICAgIC8vIFRyeSB0byB1c2UgYnJvd3NlciBiYWNrIGlmIHRoZXJlJ3MgaGlzdG9yeVxuICAgICAgY29uc3QgYmFja1VybCA9IGdldEJhY2tVcmwoKTtcbiAgICAgIGNvbnN0IHJlZmVycmVyID0gZG9jdW1lbnQucmVmZXJyZXI7XG4gICAgICBjb25zdCBjdXJyZW50SG9zdCA9IHdpbmRvdy5sb2NhdGlvbi5ob3N0O1xuXG4gICAgICAvLyBJZiByZWZlcnJlciBpcyBmcm9tIG91ciBhcHAsIHVzZSBicm93c2VyIGJhY2sgZm9yIGJldHRlciBVWFxuICAgICAgaWYgKHJlZmVycmVyICYmIHJlZmVycmVyLmluY2x1ZGVzKGN1cnJlbnRIb3N0KSkge1xuICAgICAgICByb3V0ZXIuYmFjaygpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gRmFsbGJhY2sgdG8gcHJvZ3JhbW1hdGljIG5hdmlnYXRpb25cbiAgICByb3V0ZXIucHVzaChnZXRCYWNrVXJsKCkpO1xuICB9O1xuXG4gIGNvbnN0IFtjb25maWdEZXRhaWxzLCBzZXRDb25maWdEZXRhaWxzXSA9IHVzZVN0YXRlPEN1c3RvbUFwaUNvbmZpZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGU8Ym9vbGVhbj4odHJ1ZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzdWNjZXNzTWVzc2FnZSwgc2V0U3VjY2Vzc01lc3NhZ2VdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzaG93T3B0aW1pc3RpY0xvYWRpbmcsIHNldFNob3dPcHRpbWlzdGljTG9hZGluZ10gPSB1c2VTdGF0ZTxib29sZWFuPihmYWxzZSk7XG5cbiAgY29uc3QgW3NlbGVjdGVkU3RyYXRlZ3ksIHNldFNlbGVjdGVkU3RyYXRlZ3ldID0gdXNlU3RhdGU8Um91dGluZ1N0cmF0ZWd5VHlwZT4oJ25vbmUnKTtcbiAgY29uc3QgW3N0cmF0ZWd5UGFyYW1zLCBzZXRTdHJhdGVneVBhcmFtc10gPSB1c2VTdGF0ZTxhbnk+KHt9KTtcblxuICBjb25zdCBbYXZhaWxhYmxlQXBpS2V5cywgc2V0QXZhaWxhYmxlQXBpS2V5c10gPSB1c2VTdGF0ZTxEaXNwbGF5QXBpS2V5W10+KFtdKTtcbiAgY29uc3QgW2lzTG9hZGluZ0FwaUtleXMsIHNldElzTG9hZGluZ0FwaUtleXNdID0gdXNlU3RhdGU8Ym9vbGVhbj4oZmFsc2UpO1xuXG4gIC8vIFN0YXRlIHNwZWNpZmljYWxseSBmb3Igc3RyaWN0X2ZhbGxiYWNrIHN0cmF0ZWd5XG4gIGNvbnN0IFtvcmRlcmVkRmFsbGJhY2tLZXlzLCBzZXRPcmRlcmVkRmFsbGJhY2tLZXlzXSA9IHVzZVN0YXRlPERpc3BsYXlBcGlLZXlbXT4oW10pO1xuXG4gIC8vIE5ldyBTdGF0ZSBmb3IgQ29tcGxleGl0eS1CYXNlZCBSb3VuZC1Sb2JpblxuICBjb25zdCBbc2VsZWN0ZWRBcGlLZXlGb3JDb21wbGV4aXR5LCBzZXRTZWxlY3RlZEFwaUtleUZvckNvbXBsZXhpdHldID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIC8vIFN0b3JlcyBmZXRjaGVkIGFzc2lnbm1lbnRzOiB7IGFwaUtleUlkMTogWzEsMl0sIGFwaUtleUlkMjogWzNdIH1cbiAgY29uc3QgW2NvbXBsZXhpdHlBc3NpZ25tZW50cywgc2V0Q29tcGxleGl0eUFzc2lnbm1lbnRzXSA9IHVzZVN0YXRlPFJlY29yZDxzdHJpbmcsIG51bWJlcltdPj4oe30pOyBcbiAgLy8gTGV2ZWxzIHNlbGVjdGVkIGluIFVJIGZvciB0aGUgY3VycmVudCBzZWxlY3RlZEFwaUtleUZvckNvbXBsZXhpdHlcbiAgY29uc3QgW2N1cnJlbnRLZXlDb21wbGV4aXR5TGV2ZWxzLCBzZXRDdXJyZW50S2V5Q29tcGxleGl0eUxldmVsc10gPSB1c2VTdGF0ZTxudW1iZXJbXT4oW10pOyBcbiAgY29uc3QgW2lzRmV0Y2hpbmdBc3NpZ25tZW50cywgc2V0SXNGZXRjaGluZ0Fzc2lnbm1lbnRzXSA9IHVzZVN0YXRlPGJvb2xlYW4+KGZhbHNlKTtcbiAgY29uc3QgW2lzU2F2aW5nQXNzaWdubWVudHMsIHNldElzU2F2aW5nQXNzaWdubWVudHNdID0gdXNlU3RhdGU8Ym9vbGVhbj4oZmFsc2UpO1xuICBjb25zdCBbYXNzaWdubWVudHNFcnJvciwgc2V0QXNzaWdubWVudHNFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2Fzc2lnbm1lbnRzU3VjY2Vzc01lc3NhZ2UsIHNldEFzc2lnbm1lbnRzU3VjY2Vzc01lc3NhZ2VdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG5cbiAgLy8gVGVtcG9yYXJpbHkgZGlzYWJsZWQgRG5EIHNlbnNvcnMgYW5kIGhhbmRsZXJzXG4gIC8vIGNvbnN0IHNlbnNvcnMgPSB1c2VTZW5zb3JzKFxuICAvLyAgIHVzZVNlbnNvcihQb2ludGVyU2Vuc29yKSxcbiAgLy8gICB1c2VTZW5zb3IoS2V5Ym9hcmRTZW5zb3IsIHtcbiAgLy8gICAgIGNvb3JkaW5hdGVHZXR0ZXI6IHNvcnRhYmxlS2V5Ym9hcmRDb29yZGluYXRlcyxcbiAgLy8gICB9KVxuICAvLyApO1xuXG4gIC8vIGNvbnN0IGhhbmRsZURyYWdFbmQgPSAoZXZlbnQ6IERyYWdFbmRFdmVudCkgPT4ge1xuICAvLyAgIGNvbnN0IHsgYWN0aXZlLCBvdmVyIH0gPSBldmVudDtcblxuICAvLyAgIGlmIChhY3RpdmUuaWQgIT09IG92ZXI/LmlkKSB7XG4gIC8vICAgICBjb25zdCBvbGRJbmRleCA9IG9yZGVyZWRGYWxsYmFja0tleXMuZmluZEluZGV4KChrZXkpID0+IGtleS5pZCA9PT0gYWN0aXZlLmlkKTtcbiAgLy8gICAgIGNvbnN0IG5ld0luZGV4ID0gb3JkZXJlZEZhbGxiYWNrS2V5cy5maW5kSW5kZXgoKGtleSkgPT4ga2V5LmlkID09PSBvdmVyPy5pZCk7XG5cbiAgLy8gICAgIGNvbnN0IG5ld09yZGVyZWRLZXlzID0gYXJyYXlNb3ZlKG9yZGVyZWRGYWxsYmFja0tleXMsIG9sZEluZGV4LCBuZXdJbmRleCk7XG4gIC8vICAgICBzZXRPcmRlcmVkRmFsbGJhY2tLZXlzKG5ld09yZGVyZWRLZXlzKTtcbiAgLy8gICAgIC8vIFVwZGF0ZSBzdHJhdGVneVBhcmFtcyBpbW1lZGlhdGVseSBmb3Igc2F2aW5nXG4gIC8vICAgICBzZXRTdHJhdGVneVBhcmFtcyh7IG9yZGVyZWRfYXBpX2tleV9pZHM6IG5ld09yZGVyZWRLZXlzLm1hcChrID0+IGsuaWQpIH0pO1xuICAvLyAgIH1cbiAgLy8gfTtcblxuICBjb25zdCBmZXRjaENvbmZpZ0FuZEtleXMgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFjb25maWdJZCkge1xuICAgICAgc2V0RXJyb3IoJ0NvbmZpZ3VyYXRpb24gSUQgaXMgbWlzc2luZy4nKTtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgZm9yIGNhY2hlZCBkYXRhIGZpcnN0XG4gICAgY29uc3QgY2FjaGVkRGF0YSA9IGdldENhY2hlZERhdGEoY29uZmlnSWQpO1xuICAgIGlmIChjYWNoZWREYXRhICYmIGNhY2hlZERhdGEuY29uZmlnRGV0YWlscyAmJiBjYWNoZWREYXRhLmFwaUtleXMpIHtcbiAgICAgIGNvbnNvbGUubG9nKGDimqEgW1JPVVRJTkcgU0VUVVBdIFVzaW5nIGNhY2hlZCBkYXRhIGZvciBjb25maWc6ICR7Y29uZmlnSWR9YCk7XG5cbiAgICAgIHNldENvbmZpZ0RldGFpbHMoY2FjaGVkRGF0YS5jb25maWdEZXRhaWxzKTtcbiAgICAgIHNldEF2YWlsYWJsZUFwaUtleXMoY2FjaGVkRGF0YS5hcGlLZXlzKTtcblxuICAgICAgY29uc3QgY3VycmVudFNlbGVjdGVkU3RyYXRlZ3kgPSAoY2FjaGVkRGF0YS5yb3V0aW5nU3RyYXRlZ3kgYXMgUm91dGluZ1N0cmF0ZWd5VHlwZSkgfHwgJ25vbmUnO1xuICAgICAgc2V0U2VsZWN0ZWRTdHJhdGVneShjdXJyZW50U2VsZWN0ZWRTdHJhdGVneSk7XG4gICAgICBzZXRTdHJhdGVneVBhcmFtcyhjYWNoZWREYXRhLnJvdXRpbmdQYXJhbXMgfHwge30pO1xuXG4gICAgICAvLyBJbml0aWFsaXplIG9yZGVyZWRGYWxsYmFja0tleXMgYmFzZWQgb24gY2FjaGVkIGRhdGFcbiAgICAgIGlmIChjdXJyZW50U2VsZWN0ZWRTdHJhdGVneSA9PT0gJ3N0cmljdF9mYWxsYmFjaycgJiYgY2FjaGVkRGF0YS5yb3V0aW5nUGFyYW1zPy5vcmRlcmVkX2FwaV9rZXlfaWRzKSB7XG4gICAgICAgIGNvbnN0IHNhdmVkT3JkZXIgPSBjYWNoZWREYXRhLnJvdXRpbmdQYXJhbXMub3JkZXJlZF9hcGlfa2V5X2lkcyBhcyBzdHJpbmdbXTtcbiAgICAgICAgY29uc3QgcmVvcmRlcmVkS2V5cyA9IHNhdmVkT3JkZXIubWFwKGlkID0+IGNhY2hlZERhdGEuYXBpS2V5cy5maW5kKGsgPT4gay5pZCA9PT0gaWQpKS5maWx0ZXIoQm9vbGVhbikgYXMgRGlzcGxheUFwaUtleVtdO1xuICAgICAgICBjb25zdCByZW1haW5pbmdLZXlzID0gY2FjaGVkRGF0YS5hcGlLZXlzLmZpbHRlcihrID0+ICFzYXZlZE9yZGVyLmluY2x1ZGVzKGsuaWQpKTtcbiAgICAgICAgc2V0T3JkZXJlZEZhbGxiYWNrS2V5cyhbLi4ucmVvcmRlcmVkS2V5cywgLi4ucmVtYWluaW5nS2V5c10pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0T3JkZXJlZEZhbGxiYWNrS2V5cyhbLi4uY2FjaGVkRGF0YS5hcGlLZXlzXSk7XG4gICAgICB9XG5cbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgICBzZXRJc0xvYWRpbmdBcGlLZXlzKGZhbHNlKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyBTaG93IG9wdGltaXN0aWMgbG9hZGluZyBmb3IgZmlyc3QtdGltZSB2aXNpdHNcbiAgICBpZiAoIWlzQ2FjaGVkKGNvbmZpZ0lkKSkge1xuICAgICAgc2V0U2hvd09wdGltaXN0aWNMb2FkaW5nKHRydWUpO1xuICAgIH1cblxuICAgIHNldElzTG9hZGluZyh0cnVlKTtcbiAgICBzZXRJc0xvYWRpbmdBcGlLZXlzKHRydWUpO1xuICAgIHNldEVycm9yKG51bGwpO1xuICAgIHNldFN1Y2Nlc3NNZXNzYWdlKG51bGwpO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIEZldGNoIGNvbmZpZyBkZXRhaWxzXG4gICAgICBjb25zdCBjb25maWdSZXMgPSBhd2FpdCBmZXRjaChgL2FwaS9jdXN0b20tY29uZmlncy8ke2NvbmZpZ0lkfWApO1xuICAgICAgaWYgKCFjb25maWdSZXMub2spIHtcbiAgICAgICAgY29uc3QgZXJyRGF0YSA9IGF3YWl0IGNvbmZpZ1Jlcy5qc29uKCk7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJEYXRhLmVycm9yIHx8ICdGYWlsZWQgdG8gZmV0Y2ggY29uZmlndXJhdGlvbicpO1xuICAgICAgfVxuICAgICAgY29uc3QgY3VycmVudENvbmZpZzogQ3VzdG9tQXBpQ29uZmlnID0gYXdhaXQgY29uZmlnUmVzLmpzb24oKTtcbiAgICAgIHNldENvbmZpZ0RldGFpbHMoY3VycmVudENvbmZpZyk7XG4gICAgICBjb25zdCBjdXJyZW50U2VsZWN0ZWRTdHJhdGVneSA9IChjdXJyZW50Q29uZmlnLnJvdXRpbmdfc3RyYXRlZ3kgYXMgUm91dGluZ1N0cmF0ZWd5VHlwZSkgfHwgJ25vbmUnO1xuICAgICAgc2V0U2VsZWN0ZWRTdHJhdGVneShjdXJyZW50U2VsZWN0ZWRTdHJhdGVneSk7XG4gICAgICBjb25zdCBjdXJyZW50U3RyYXRlZ3lQYXJhbXMgPSBjdXJyZW50Q29uZmlnLnJvdXRpbmdfc3RyYXRlZ3lfcGFyYW1zIHx8IHt9O1xuICAgICAgc2V0U3RyYXRlZ3lQYXJhbXMoY3VycmVudFN0cmF0ZWd5UGFyYW1zKTsgLy8gR2VuZXJhbCBwYXJhbXMgc3RhdGVcblxuICAgICAgLy8gRmV0Y2ggQVBJIGtleXMgZm9yIHRoaXMgY29uZmlndXJhdGlvblxuICAgICAgY29uc3Qga2V5c1JlcyA9IGF3YWl0IGZldGNoKGAvYXBpL2tleXM/Y3VzdG9tX2NvbmZpZ19pZD0ke2NvbmZpZ0lkfWApO1xuICAgICAgaWYgKCFrZXlzUmVzLm9rKSB7XG4gICAgICAgIGNvbnN0IGVyckRhdGEgPSBhd2FpdCBrZXlzUmVzLmpzb24oKTtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGVyckRhdGEuZXJyb3IgfHwgJ0ZhaWxlZCB0byBmZXRjaCBBUEkga2V5cyBmb3IgdGhpcyBjb25maWd1cmF0aW9uJyk7XG4gICAgICB9XG4gICAgICBjb25zdCBrZXlzOiBEaXNwbGF5QXBpS2V5W10gPSBhd2FpdCBrZXlzUmVzLmpzb24oKTtcbiAgICAgIHNldEF2YWlsYWJsZUFwaUtleXMoa2V5cyk7XG5cbiAgICAgIC8vIEluaXRpYWxpemUgb3JkZXJlZEZhbGxiYWNrS2V5cyBiYXNlZCBvbiBmZXRjaGVkIGtleXMgYW5kIHNhdmVkIHBhcmFtc1xuICAgICAgaWYgKGN1cnJlbnRTZWxlY3RlZFN0cmF0ZWd5ID09PSAnc3RyaWN0X2ZhbGxiYWNrJyAmJiBjdXJyZW50U3RyYXRlZ3lQYXJhbXMub3JkZXJlZF9hcGlfa2V5X2lkcykge1xuICAgICAgICBjb25zdCBzYXZlZE9yZGVyID0gY3VycmVudFN0cmF0ZWd5UGFyYW1zLm9yZGVyZWRfYXBpX2tleV9pZHMgYXMgc3RyaW5nW107XG4gICAgICAgIGNvbnN0IHJlb3JkZXJlZEtleXMgPSBzYXZlZE9yZGVyLm1hcChpZCA9PiBrZXlzLmZpbmQoayA9PiBrLmlkID09PSBpZCkpLmZpbHRlcihCb29sZWFuKSBhcyBEaXNwbGF5QXBpS2V5W107XG4gICAgICAgIC8vIEFkZCBhbnkga2V5cyBwcmVzZW50IGluIGBrZXlzYCBidXQgbm90IGluIGBzYXZlZE9yZGVyYCB0byB0aGUgZW5kXG4gICAgICAgIGNvbnN0IHJlbWFpbmluZ0tleXMgPSBrZXlzLmZpbHRlcihrID0+ICFzYXZlZE9yZGVyLmluY2x1ZGVzKGsuaWQpKTtcbiAgICAgICAgc2V0T3JkZXJlZEZhbGxiYWNrS2V5cyhbLi4ucmVvcmRlcmVkS2V5cywgLi4ucmVtYWluaW5nS2V5c10pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0T3JkZXJlZEZhbGxiYWNrS2V5cyhbLi4ua2V5c10pOyAvLyBEZWZhdWx0IG9yZGVyIG9yIGZvciBvdGhlciBzdHJhdGVnaWVzIGluaXRpYWxseVxuICAgICAgfVxuXG4gICAgfSBjYXRjaCAoZXJyOiBhbnkpIHtcbiAgICAgIHNldEVycm9yKGBFcnJvciBsb2FkaW5nIGRhdGE6ICR7ZXJyLm1lc3NhZ2V9YCk7XG4gICAgICBzZXRDb25maWdEZXRhaWxzKG51bGwpO1xuICAgICAgc2V0QXZhaWxhYmxlQXBpS2V5cyhbXSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgICBzZXRJc0xvYWRpbmdBcGlLZXlzKGZhbHNlKTtcbiAgICAgIHNldFNob3dPcHRpbWlzdGljTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9LCBbY29uZmlnSWQsIGdldENhY2hlZERhdGEsIGlzQ2FjaGVkXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaENvbmZpZ0FuZEtleXMoKTtcbiAgfSwgW2ZldGNoQ29uZmlnQW5kS2V5c10pO1xuXG4gIC8vIEZldGNoIGNvbXBsZXhpdHkgYXNzaWdubWVudHMgZm9yIHRoZSBzZWxlY3RlZCBBUEkga2V5XG4gIGNvbnN0IGZldGNoQ29tcGxleGl0eUFzc2lnbm1lbnRzRm9yS2V5ID0gdXNlQ2FsbGJhY2soYXN5bmMgKGFwaUtleUlkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIWNvbmZpZ0lkIHx8ICFhcGlLZXlJZCkgcmV0dXJuO1xuICAgIHNldElzRmV0Y2hpbmdBc3NpZ25tZW50cyh0cnVlKTtcbiAgICBzZXRBc3NpZ25tZW50c0Vycm9yKG51bGwpO1xuICAgIHNldEFzc2lnbm1lbnRzU3VjY2Vzc01lc3NhZ2UobnVsbCk7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvY3VzdG9tLWNvbmZpZ3MvJHtjb25maWdJZH0va2V5cy8ke2FwaUtleUlkfS9jb21wbGV4aXR5LWFzc2lnbm1lbnRzYCk7XG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5lcnJvciB8fCAnRmFpbGVkIHRvIGZldGNoIGNvbXBsZXhpdHkgYXNzaWdubWVudHMnKTtcbiAgICAgIH1cbiAgICAgIGNvbnN0IGZldGNoZWRMZXZlbHM6IG51bWJlcltdID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgc2V0Q29tcGxleGl0eUFzc2lnbm1lbnRzKHByZXYgPT4gKHsgLi4ucHJldiwgW2FwaUtleUlkXTogZmV0Y2hlZExldmVscyB9KSk7XG4gICAgICBzZXRDdXJyZW50S2V5Q29tcGxleGl0eUxldmVscyhmZXRjaGVkTGV2ZWxzKTtcbiAgICB9IGNhdGNoIChlcnI6IGFueSkge1xuICAgICAgc2V0QXNzaWdubWVudHNFcnJvcihgRXJyb3IgZmV0Y2hpbmcgYXNzaWdubWVudHMgZm9yIGtleTogJHtlcnIubWVzc2FnZX1gKTtcbiAgICAgIHNldEN1cnJlbnRLZXlDb21wbGV4aXR5TGV2ZWxzKFtdKTsgLy8gUmVzZXQgb24gZXJyb3JcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNGZXRjaGluZ0Fzc2lnbm1lbnRzKGZhbHNlKTtcbiAgICB9XG4gIH0sIFtjb25maWdJZF0pO1xuXG4gIC8vIEVmZmVjdCB0byBmZXRjaCBhc3NpZ25tZW50cyB3aGVuIHNlbGVjdGVkQXBpS2V5Rm9yQ29tcGxleGl0eSBjaGFuZ2VzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHNlbGVjdGVkQXBpS2V5Rm9yQ29tcGxleGl0eSkge1xuICAgICAgZmV0Y2hDb21wbGV4aXR5QXNzaWdubWVudHNGb3JLZXkoc2VsZWN0ZWRBcGlLZXlGb3JDb21wbGV4aXR5KTtcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gQ2xlYXIgbGV2ZWxzIGlmIG5vIGtleSBpcyBzZWxlY3RlZFxuICAgICAgc2V0Q3VycmVudEtleUNvbXBsZXhpdHlMZXZlbHMoW10pO1xuICAgICAgc2V0QXNzaWdubWVudHNFcnJvcihudWxsKTtcbiAgICB9XG4gIH0sIFtzZWxlY3RlZEFwaUtleUZvckNvbXBsZXhpdHksIGZldGNoQ29tcGxleGl0eUFzc2lnbm1lbnRzRm9yS2V5XSk7XG5cbiAgLy8gSGFuZGxlIGNoZWNrYm94IGNoYW5nZSBmb3IgY29tcGxleGl0eSBsZXZlbHNcbiAgY29uc3QgaGFuZGxlQ29tcGxleGl0eUxldmVsQ2hhbmdlID0gKGxldmVsOiBudW1iZXIsIGNoZWNrZWQ6IGJvb2xlYW4pID0+IHtcbiAgICBzZXRDdXJyZW50S2V5Q29tcGxleGl0eUxldmVscyhwcmV2TGV2ZWxzID0+IHtcbiAgICAgIGlmIChjaGVja2VkKSB7XG4gICAgICAgIHJldHVybiBbLi4ucHJldkxldmVscywgbGV2ZWxdLnNvcnQoKGEsIGIpID0+IGEgLSBiKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHJldHVybiBwcmV2TGV2ZWxzLmZpbHRlcihsID0+IGwgIT09IGxldmVsKTtcbiAgICAgIH1cbiAgICB9KTtcbiAgfTtcblxuICAvLyBTYXZlIGNvbXBsZXhpdHkgYXNzaWdubWVudHMgZm9yIHRoZSBzZWxlY3RlZCBBUEkga2V5XG4gIGNvbnN0IGhhbmRsZVNhdmVDb21wbGV4aXR5QXNzaWdubWVudHMgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFjb25maWdJZCB8fCAhc2VsZWN0ZWRBcGlLZXlGb3JDb21wbGV4aXR5KSB7XG4gICAgICBzZXRBc3NpZ25tZW50c0Vycm9yKCdObyBBUEkga2V5IHNlbGVjdGVkIHRvIHNhdmUgYXNzaWdubWVudHMgZm9yLicpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBzZXRJc1NhdmluZ0Fzc2lnbm1lbnRzKHRydWUpO1xuICAgIHNldEFzc2lnbm1lbnRzRXJyb3IobnVsbCk7XG4gICAgc2V0QXNzaWdubWVudHNTdWNjZXNzTWVzc2FnZShudWxsKTtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9jdXN0b20tY29uZmlncy8ke2NvbmZpZ0lkfS9rZXlzLyR7c2VsZWN0ZWRBcGlLZXlGb3JDb21wbGV4aXR5fS9jb21wbGV4aXR5LWFzc2lnbm1lbnRzYCwge1xuICAgICAgICBtZXRob2Q6ICdQVVQnLFxuICAgICAgICBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBjb21wbGV4aXR5X2xldmVsczogY3VycmVudEtleUNvbXBsZXhpdHlMZXZlbHMgfSksXG4gICAgICB9KTtcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLmVycm9yIHx8ICdGYWlsZWQgdG8gc2F2ZSBjb21wbGV4aXR5IGFzc2lnbm1lbnRzJyk7XG4gICAgICB9XG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAvLyBVcGRhdGUgdGhlIG1haW4gc3RvcmUgb2YgYXNzaWdubWVudHMgZm9yIHRoaXMga2V5XG4gICAgICBzZXRDb21wbGV4aXR5QXNzaWdubWVudHMocHJldiA9PiAoeyAuLi5wcmV2LCBbc2VsZWN0ZWRBcGlLZXlGb3JDb21wbGV4aXR5XTogWy4uLmN1cnJlbnRLZXlDb21wbGV4aXR5TGV2ZWxzXSB9KSk7XG4gICAgICBzZXRBc3NpZ25tZW50c1N1Y2Nlc3NNZXNzYWdlKHJlc3VsdC5tZXNzYWdlIHx8ICdDb21wbGV4aXR5IGFzc2lnbm1lbnRzIHNhdmVkIHN1Y2Nlc3NmdWxseSEnKTtcbiAgICB9IGNhdGNoIChlcnI6IGFueSkge1xuICAgICAgc2V0QXNzaWdubWVudHNFcnJvcihgRXJyb3Igc2F2aW5nIGFzc2lnbm1lbnRzOiAke2Vyci5tZXNzYWdlfWApO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc1NhdmluZ0Fzc2lnbm1lbnRzKGZhbHNlKTtcbiAgICB9XG4gIH0sIFtjb25maWdJZCwgc2VsZWN0ZWRBcGlLZXlGb3JDb21wbGV4aXR5LCBjdXJyZW50S2V5Q29tcGxleGl0eUxldmVsc10pO1xuXG4gIC8vIEhhbmRsZSBkcmFnIGFuZCBkcm9wIHJlb3JkZXJpbmcgZm9yIHN0cmljdCBmYWxsYmFja1xuICBjb25zdCBoYW5kbGVEcmFnUmVvcmRlciA9IChuZXdPcmRlcjogRGlzcGxheUFwaUtleVtdKSA9PiB7XG4gICAgc2V0T3JkZXJlZEZhbGxiYWNrS2V5cyhuZXdPcmRlcik7XG4gICAgLy8gVXBkYXRlIHN0cmF0ZWd5UGFyYW1zIGltbWVkaWF0ZWx5IGZvciBzYXZpbmdcbiAgICBzZXRTdHJhdGVneVBhcmFtcyh7IG9yZGVyZWRfYXBpX2tleV9pZHM6IG5ld09yZGVyLm1hcChrID0+IGsuaWQpIH0pO1xuICB9O1xuXG4gIGNvbnN0IG1vdmVGYWxsYmFja0tleSA9IChpbmRleDogbnVtYmVyLCBkaXJlY3Rpb246ICd1cCcgfCAnZG93bicpID0+IHtcbiAgICBjb25zdCBuZXdPcmRlcmVkS2V5cyA9IFsuLi5vcmRlcmVkRmFsbGJhY2tLZXlzXTtcbiAgICBjb25zdCBrZXlUb01vdmUgPSBuZXdPcmRlcmVkS2V5c1tpbmRleF07XG4gICAgaWYgKGRpcmVjdGlvbiA9PT0gJ3VwJyAmJiBpbmRleCA+IDApIHtcbiAgICAgIG5ld09yZGVyZWRLZXlzLnNwbGljZShpbmRleCwgMSk7XG4gICAgICBuZXdPcmRlcmVkS2V5cy5zcGxpY2UoaW5kZXggLSAxLCAwLCBrZXlUb01vdmUpO1xuICAgIH0gZWxzZSBpZiAoZGlyZWN0aW9uID09PSAnZG93bicgJiYgaW5kZXggPCBuZXdPcmRlcmVkS2V5cy5sZW5ndGggLSAxKSB7XG4gICAgICBuZXdPcmRlcmVkS2V5cy5zcGxpY2UoaW5kZXgsIDEpO1xuICAgICAgbmV3T3JkZXJlZEtleXMuc3BsaWNlKGluZGV4ICsgMSwgMCwga2V5VG9Nb3ZlKTtcbiAgICB9XG4gICAgc2V0T3JkZXJlZEZhbGxiYWNrS2V5cyhuZXdPcmRlcmVkS2V5cyk7XG4gICAgLy8gVXBkYXRlIHN0cmF0ZWd5UGFyYW1zIGltbWVkaWF0ZWx5IGZvciBzYXZpbmdcbiAgICBzZXRTdHJhdGVneVBhcmFtcyh7IG9yZGVyZWRfYXBpX2tleV9pZHM6IG5ld09yZGVyZWRLZXlzLm1hcChrID0+IGsuaWQpIH0pO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVNhdmVSb3V0aW5nU2V0dGluZ3MgPSBhc3luYyAoZTogRm9ybUV2ZW50PEhUTUxGb3JtRWxlbWVudD4pID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgaWYgKCFjb25maWdJZCB8fCAhY29uZmlnRGV0YWlscykge1xuICAgICAgc2V0RXJyb3IoJ0NvbmZpZ3VyYXRpb24gZGV0YWlscyBub3QgbG9hZGVkLicpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XG4gICAgc2V0RXJyb3IobnVsbCk7XG4gICAgc2V0U3VjY2Vzc01lc3NhZ2UobnVsbCk7XG5cbiAgICBsZXQgcGFyYW1zVG9TYXZlID0gc3RyYXRlZ3lQYXJhbXM7XG4gICAgaWYgKHNlbGVjdGVkU3RyYXRlZ3kgPT09ICdzdHJpY3RfZmFsbGJhY2snKSB7XG4gICAgICBwYXJhbXNUb1NhdmUgPSB7IG9yZGVyZWRfYXBpX2tleV9pZHM6IG9yZGVyZWRGYWxsYmFja0tleXMubWFwKGsgPT4gay5pZCkgfTtcbiAgICB9XG4gICAgLy8gQWRkIHNpbWlsYXIgbG9naWMgZm9yIG90aGVyIHN0cmF0ZWdpZXMgdG8gc3RydWN0dXJlIHRoZWlyIHBhcmFtc1RvU2F2ZVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvY3VzdG9tLWNvbmZpZ3MvJHtjb25maWdJZH0vcm91dGluZ2AsIHtcbiAgICAgICAgbWV0aG9kOiAnUFVUJyxcbiAgICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICByb3V0aW5nX3N0cmF0ZWd5OiBzZWxlY3RlZFN0cmF0ZWd5LFxuICAgICAgICAgIHJvdXRpbmdfc3RyYXRlZ3lfcGFyYW1zOiBwYXJhbXNUb1NhdmUsIFxuICAgICAgICB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5lcnJvciB8fCAnRmFpbGVkIHRvIHNhdmUgcm91dGluZyBzZXR0aW5ncycpO1xuICAgICAgfVxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgc2V0U3VjY2Vzc01lc3NhZ2UocmVzdWx0Lm1lc3NhZ2UgfHwgJ1JvdXRpbmcgc2V0dGluZ3Mgc2F2ZWQgc3VjY2Vzc2Z1bGx5IScpO1xuICAgICAgc2V0Q29uZmlnRGV0YWlscyhwcmV2ID0+IHByZXYgPyB7IC4uLnByZXYsIHJvdXRpbmdfc3RyYXRlZ3k6IHNlbGVjdGVkU3RyYXRlZ3ksIHJvdXRpbmdfc3RyYXRlZ3lfcGFyYW1zOiBwYXJhbXNUb1NhdmUgfSA6IG51bGwpO1xuICAgICAgLy8gVXBkYXRlIGxvY2FsIHBhcmFtcyB0byByZWZsZWN0IHNhdmVkIHN0YXRlIGlmIG5lY2Vzc2FyeSwgZS5nLiBpZiBiYWNrZW5kIHRyYW5zZm9ybXMgaXRcbiAgICAgIHNldFN0cmF0ZWd5UGFyYW1zKHBhcmFtc1RvU2F2ZSk7IFxuXG4gICAgfSBjYXRjaCAoZXJyOiBhbnkpIHtcbiAgICAgIHNldEVycm9yKGBFcnJvciBzYXZpbmcgc2V0dGluZ3M6ICR7ZXJyLm1lc3NhZ2V9YCk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHJlbmRlckNvbXBsZXhpdHlBc3NpZ25tZW50VUkgPSAoKSA9PiB7XG4gICAgaWYgKHNlbGVjdGVkU3RyYXRlZ3kgIT09ICdjb21wbGV4aXR5X3JvdW5kX3JvYmluJykgcmV0dXJuIG51bGw7XG5cbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC04IHB0LTYgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+Q29tcGxleGl0eS1CYXNlZCBLZXkgQXNzaWdubWVudHM8L2gzPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbWItNlwiPlxuICAgICAgICAgIEFzc2lnbiBBUEkga2V5cyB0byBoYW5kbGUgcHJvbXB0cyBvZiBzcGVjaWZpYyBjb21wbGV4aXR5IGxldmVscyAoMS01KS4gVGhlIHN5c3RlbSB3aWxsIGNsYXNzaWZ5IGluY29taW5nIHByb21wdHMgYW5kIHJvdW5kLXJvYmluIHJlcXVlc3RzIGFtb25nIGtleXMgYXNzaWduZWQgdG8gdGhhdCBjb21wbGV4aXR5LlxuICAgICAgICA8L3A+XG5cbiAgICAgICAge2lzTG9hZGluZ0FwaUtleXMgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcHktNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtNSB3LTUgYm9yZGVyLWItMiBib3JkZXItb3JhbmdlLTYwMFwiPjwvZGl2PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIG1sLTJcIj5Mb2FkaW5nIEFQSSBrZXlzLi4uPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgICB7IWlzTG9hZGluZ0FwaUtleXMgJiYgYXZhaWxhYmxlQXBpS2V5cy5sZW5ndGggPT09IDAgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmcteWVsbG93LTUwIGJvcmRlciBib3JkZXIteWVsbG93LTIwMCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXllbGxvdy04MDBcIj5ObyBBUEkga2V5cyBmb3VuZCBmb3IgdGhpcyBjb25maWd1cmF0aW9uLiBQbGVhc2UgYWRkIEFQSSBrZXlzIGZpcnN0IG9uIHRoZSBtb2RlbCBjb25maWd1cmF0aW9uIHBhZ2UuPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHthdmFpbGFibGVBcGlLZXlzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJhcGlLZXlGb3JDb21wbGV4aXR5XCIgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5TZWxlY3QgQVBJIEtleSB0byBBc3NpZ24gQ29tcGxleGl0aWVzOjwvbGFiZWw+XG4gICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgIGlkPVwiYXBpS2V5Rm9yQ29tcGxleGl0eVwiXG4gICAgICAgICAgICAgIHZhbHVlPXtzZWxlY3RlZEFwaUtleUZvckNvbXBsZXhpdHkgfHwgJyd9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VsZWN0ZWRBcGlLZXlGb3JDb21wbGV4aXR5KGUudGFyZ2V0LnZhbHVlIHx8IG51bGwpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmb3JtLXNlbGVjdCBtYXgtdy1tZFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIiBkaXNhYmxlZD4tLSBTZWxlY3QgYW4gQVBJIEtleSAtLTwvb3B0aW9uPlxuICAgICAgICAgICAgICB7YXZhaWxhYmxlQXBpS2V5cy5tYXAoa2V5ID0+IChcbiAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17a2V5LmlkfSB2YWx1ZT17a2V5LmlkfT57a2V5LmxhYmVsfSAoe2tleS5wcm92aWRlcn0gLSB7a2V5LnByZWRlZmluZWRfbW9kZWxfaWR9KTwvb3B0aW9uPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHtzZWxlY3RlZEFwaUtleUZvckNvbXBsZXhpdHkgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZCBwLTZcIj5cbiAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LW1kIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItNFwiPkFzc2lnbiBDb21wbGV4aXR5IExldmVscyBmb3I6IDxzcGFuIGNsYXNzTmFtZT1cInRleHQtb3JhbmdlLTYwMFwiPnthdmFpbGFibGVBcGlLZXlzLmZpbmQoayA9PiBrLmlkID09PSBzZWxlY3RlZEFwaUtleUZvckNvbXBsZXhpdHkpPy5sYWJlbH08L3NwYW4+PC9oND5cblxuICAgICAgICAgICAge2lzRmV0Y2hpbmdBc3NpZ25tZW50cyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcHktNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTUgdy01IGJvcmRlci1iLTIgYm9yZGVyLW9yYW5nZS02MDBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbWwtMlwiPkxvYWRpbmcgY3VycmVudCBhc3NpZ25tZW50cy4uLjwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7YXNzaWdubWVudHNFcnJvciAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcmVkLTUwIGJvcmRlciBib3JkZXItcmVkLTIwMCByb3VuZGVkLWxnIHAtMyBtYi00XCI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1yZWQtODAwIHRleHQtc21cIj57YXNzaWdubWVudHNFcnJvcn08L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAge2Fzc2lnbm1lbnRzU3VjY2Vzc01lc3NhZ2UgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyZWVuLTUwIGJvcmRlciBib3JkZXItZ3JlZW4tMjAwIHJvdW5kZWQtbGcgcC0zIG1iLTRcIj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTgwMCB0ZXh0LXNtXCI+e2Fzc2lnbm1lbnRzU3VjY2Vzc01lc3NhZ2V9PC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHshaXNGZXRjaGluZ0Fzc2lnbm1lbnRzICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTMgbWItNlwiPlxuICAgICAgICAgICAgICAgIHtbMSwgMiwgMywgNCwgNV0ubWFwKGxldmVsID0+IChcbiAgICAgICAgICAgICAgICAgIDxsYWJlbCBrZXk9e2xldmVsfSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgcC0zIGJnLWdyYXktNTAgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkLWxnIGhvdmVyOmJvcmRlci1ncmF5LTMwMCBjdXJzb3ItcG9pbnRlciB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtjdXJyZW50S2V5Q29tcGxleGl0eUxldmVscy5pbmNsdWRlcyhsZXZlbCl9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVDb21wbGV4aXR5TGV2ZWxDaGFuZ2UobGV2ZWwsIGUudGFyZ2V0LmNoZWNrZWQpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1vcmFuZ2UtNjAwIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkIGZvY3VzOnJpbmctb3JhbmdlLTUwMCBmb2N1czpyaW5nLTJcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5Db21wbGV4aXR5IExldmVsIHtsZXZlbH08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU2F2ZUNvbXBsZXhpdHlBc3NpZ25tZW50c31cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzU2F2aW5nQXNzaWdubWVudHMgfHwgaXNGZXRjaGluZ0Fzc2lnbm1lbnRzfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidG4tcHJpbWFyeSBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtpc1NhdmluZ0Fzc2lnbm1lbnRzID8gJ1NhdmluZyBBc3NpZ25tZW50cy4uLicgOiAnU2F2ZSBBc3NpZ25tZW50cyBmb3IgdGhpcyBLZXknfVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9O1xuXG4gIC8vIFJlbmRlciBjb25maWd1cmF0aW9uIGNvbnRlbnQgYmFzZWQgb24gc2VsZWN0ZWQgc3RyYXRlZ3lcbiAgY29uc3QgcmVuZGVyQ29uZmlndXJhdGlvbkNvbnRlbnQgPSAoKSA9PiB7XG4gICAgY29uc3Qgc2VsZWN0ZWRTdHJhdGVneURhdGEgPSBST1VUSU5HX1NUUkFURUdJRVMuZmluZChzID0+IHMuaWQgPT09IHNlbGVjdGVkU3RyYXRlZ3kpO1xuXG4gICAgaWYgKHNlbGVjdGVkU3RyYXRlZ3kgPT09ICdub25lJykge1xuICAgICAgcmV0dXJuIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xNlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yMCBoLTIwIGJnLW9yYW5nZS01MDAvMjAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNlwiPlxuICAgICAgICAgICAgPENvZzZUb290aEljb24gY2xhc3NOYW1lPVwidy0xMCBoLTEwIHRleHQtb3JhbmdlLTQwMFwiIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTRcIj5EZWZhdWx0IEJlaGF2aW9yPC9oMz5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIG1heC13LW1kIG14LWF1dG8gbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICB7c2VsZWN0ZWRTdHJhdGVneURhdGE/LmRlc2NyaXB0aW9ufVxuICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTggcC00IGJnLWdyZWVuLTkwMC8yMCBib3JkZXIgYm9yZGVyLWdyZWVuLTUwMC8zMCByb3VuZGVkLXhsIGJhY2tkcm9wLWJsdXItc21cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgIDxDaGVja0NpcmNsZUljb24gY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LWdyZWVuLTQwMFwiIC8+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tMzAwIGZvbnQtbWVkaXVtXCI+Tm8gYWRkaXRpb25hbCBzZXR1cCByZXF1aXJlZDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFNhdmUgQnV0dG9uIGZvciBEZWZhdWx0ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMTIgcHQtNiBib3JkZXItdCBib3JkZXItZ3JheS03MDAvNTBcIj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJ0bi1wcmltYXJ5IGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkXCJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzTG9hZGluZ31cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2lzTG9hZGluZyA/IChcbiAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTIgYW5pbWF0ZS1zcGluXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk00IDR2NWguNTgybTE1LjM1NiAyQTguMDAxIDguMDAxIDAgMDA0LjU4MiA5bTAgMEg5bTExIDExdi01aC0uNTgxbTAgMGE4LjAwMyA4LjAwMyAwIDAxLTE1LjM1Ny0ybTE1LjM1NyAySDE1XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgU2F2aW5nLi4uXG4gICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZUljb24gY2xhc3NOYW1lPVwidy00IGgtNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgIFNhdmUgUm91dGluZyBTZXR0aW5nc1xuICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKTtcbiAgICB9XG5cbiAgICBpZiAoc2VsZWN0ZWRTdHJhdGVneSA9PT0gJ2ludGVsbGlnZW50X3JvbGUnKSB7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTQgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPEJvbHRJY29uIGNsYXNzTmFtZT1cInctNyBoLTcgbXItMyB0ZXh0LW9yYW5nZS00MDBcIiAvPlxuICAgICAgICAgICAgICBJbnRlbGxpZ2VudCBSb2xlIFJvdXRpbmdcbiAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICB7c2VsZWN0ZWRTdHJhdGVneURhdGE/LmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS05MDAvMjAgYm9yZGVyIGJvcmRlci1ibHVlLTUwMC8zMCByb3VuZGVkLXhsIHAtNiBiYWNrZHJvcC1ibHVyLXNtXCI+XG4gICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtYmx1ZS0zMDAgbWItM1wiPkhvdyBpdCB3b3Jrczo8L2g0PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMyB0ZXh0LXNtIHRleHQtYmx1ZS0yMDBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNiBoLTYgYmctYmx1ZS01MDAvMjAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG10LTAuNSBmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1ib2xkIHRleHQtYmx1ZS00MDBcIj4xPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8cD5TeXN0ZW0gYW5hbHl6ZXMgeW91ciBwcm9tcHQgdG8gdW5kZXJzdGFuZCB0aGUgbWFpbiB0YXNrPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy02IGgtNiBiZy1ibHVlLTUwMC8yMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXQtMC41IGZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyBmb250LWJvbGQgdGV4dC1ibHVlLTQwMFwiPjI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxwPk1hdGNoZXMgdGFzayB0byByZWxldmFudCBSb0tleSBvcGVyYXRpb25hbCByb2xlcyAoZS5nLiwgJ0NvZGluZyAtIEZyb250ZW5kJywgJ0NvcHl3cml0aW5nJyk8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTYgaC02IGJnLWJsdWUtNTAwLzIwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtdC0wLjUgZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNDAwXCI+Mzwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHA+Um91dGVzIHRvIGFzc2lnbmVkIEFQSSBrZXkgb3IgZmFsbHMgYmFjayB0byAnRGVmYXVsdCBHZW5lcmFsIENoYXQgTW9kZWwnPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyZWVuLTkwMC8yMCBib3JkZXIgYm9yZGVyLWdyZWVuLTUwMC8zMCByb3VuZGVkLXhsIHAtNiBiYWNrZHJvcC1ibHVyLXNtXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGVJY29uIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1ncmVlbi00MDAgbXQtMC41IGZsZXgtc2hyaW5rLTBcIiAvPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWdyZWVuLTMwMCBtYi0yXCI+UmVhZHkgdG8gdXNlITwvaDQ+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JlZW4tMjAwIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICAgICAgICBObyBhZGRpdGlvbmFsIHNldHVwIHJlcXVpcmVkLiBGdXR1cmUgZW5oYW5jZW1lbnRzIG1heSBhbGxvdyBmdXJ0aGVyIGN1c3RvbWl6YXRpb24uXG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogU2F2ZSBCdXR0b24gKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xMiBwdC02IGJvcmRlci10IGJvcmRlci1ncmF5LTcwMC81MCBmbGV4IGp1c3RpZnktZW5kXCI+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidG4tcHJpbWFyeSBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZFwiXG4gICAgICAgICAgICAgIGRpc2FibGVkPXtpc0xvYWRpbmd9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtpc0xvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yIGFuaW1hdGUtc3BpblwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNCA0djVoLjU4Mm0xNS4zNTYgMkE4LjAwMSA4LjAwMSAwIDAwNC41ODIgOW0wIDBIOW0xMSAxMXYtNWgtLjU4MW0wIDBhOC4wMDMgOC4wMDMgMCAwMS0xNS4zNTctMm0xNS4zNTcgMkgxNVwiIC8+XG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgIFNhdmluZy4uLlxuICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGVJY29uIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICBTYXZlIFJvdXRpbmcgU2V0dGluZ3NcbiAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICk7XG4gICAgfVxuXG4gICAgaWYgKHNlbGVjdGVkU3RyYXRlZ3kgPT09ICdzdHJpY3RfZmFsbGJhY2snKSB7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTQgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPExpc3RCdWxsZXRJY29uIGNsYXNzTmFtZT1cInctNyBoLTcgbXItMyB0ZXh0LW9yYW5nZS00MDBcIiAvPlxuICAgICAgICAgICAgICBTdHJpY3QgRmFsbGJhY2sgQ29uZmlndXJhdGlvblxuICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgIHtzZWxlY3RlZFN0cmF0ZWd5RGF0YT8uZGVzY3JpcHRpb259XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7aXNMb2FkaW5nQXBpS2V5cyAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBib3JkZXItMiBib3JkZXItb3JhbmdlLTQwMC8yMCBib3JkZXItdC1vcmFuZ2UtNDAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW5cIj48L2Rpdj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBtbC0zXCI+TG9hZGluZyBBUEkga2V5cy4uLjwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7IWlzTG9hZGluZ0FwaUtleXMgJiYgYXZhaWxhYmxlQXBpS2V5cy5sZW5ndGggPT09IDAgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy15ZWxsb3ctOTAwLzIwIGJvcmRlciBib3JkZXIteWVsbG93LTUwMC8zMCByb3VuZGVkLXhsIHAtOCB0ZXh0LWNlbnRlciBiYWNrZHJvcC1ibHVyLXNtXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJnLXllbGxvdy01MDAvMjAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNFwiPlxuICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LXllbGxvdy00MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMiA5djJtMCA0aC4wMW0tNi45MzggNGgxMy44NTZjMS41NCAwIDIuNTAyLTEuNjY3IDEuNzMyLTIuNUwxMy43MzIgNGMtLjc3LS44MzMtMS45NjQtLjgzMy0yLjczMiAwTDMuNzMyIDE2LjVjLS43Ny44MzMuMTkyIDIuNSAxLjczMiAyLjV6XCIgLz5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC15ZWxsb3ctMzAwIG1iLTJcIj5ObyBBUEkgS2V5cyBGb3VuZDwvaDQ+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteWVsbG93LTIwMCBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgICBQbGVhc2UgYWRkIEFQSSBrZXlzIG9uIHRoZSBtYWluIGNvbmZpZ3VyYXRpb24gcGFnZSB0byBzZXQgdXAgZmFsbGJhY2sgb3JkZXIuXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7IWlzTG9hZGluZ0FwaUtleXMgJiYgYXZhaWxhYmxlQXBpS2V5cy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS05MDAvMjAgYm9yZGVyIGJvcmRlci1ibHVlLTUwMC8zMCByb3VuZGVkLXhsIHAtNiBiYWNrZHJvcC1ibHVyLXNtXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTYgaC02IGJnLWJsdWUtNTAwLzIwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtdC0wLjUgZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ibHVlLTQwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMyAxNmgtMXYtNGgtMW0xLTRoLjAxTTIxIDEyYTkgOSAwIDExLTE4IDAgOSA5IDAgMDExOCAwelwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS0yMDAgbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgICAgICAgIERyYWcgYW5kIGRyb3AgdG8gYXJyYW5nZSB0aGUgQVBJIGtleXMgaW4gdGhlIGRlc2lyZWQgb3JkZXIgb2YgZXhlY3V0aW9uLiBUaGUgcm91dGVyIHdpbGwgdHJ5IHRoZSBmaXJzdCBrZXksIHRoZW4gdGhlIHNlY29uZCBpZiB0aGUgZmlyc3QgZmFpbHMsIGFuZCBzbyBvbi5cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIERyYWdnYWJsZSBsaXN0IGZvciByZW9yZGVyaW5nICovfVxuICAgICAgICAgICAgICA8UmVvcmRlci5Hcm91cFxuICAgICAgICAgICAgICAgIGF4aXM9XCJ5XCJcbiAgICAgICAgICAgICAgICB2YWx1ZXM9e29yZGVyZWRGYWxsYmFja0tleXN9XG4gICAgICAgICAgICAgICAgb25SZW9yZGVyPXtoYW5kbGVEcmFnUmVvcmRlcn1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJzcGFjZS15LTNcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge29yZGVyZWRGYWxsYmFja0tleXMubWFwKChhcGlLZXksIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8RHJhZ2dhYmxlQXBpS2V5SXRlbVxuICAgICAgICAgICAgICAgICAgICBrZXk9e2FwaUtleS5pZH1cbiAgICAgICAgICAgICAgICAgICAgYXBpS2V5PXthcGlLZXl9XG4gICAgICAgICAgICAgICAgICAgIGluZGV4PXtpbmRleH1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvUmVvcmRlci5Hcm91cD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogU2F2ZSBCdXR0b24gKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xMiBwdC02IGJvcmRlci10IGJvcmRlci1ncmF5LTcwMC81MCBmbGV4IGp1c3RpZnktZW5kXCI+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidG4tcHJpbWFyeSBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZFwiXG4gICAgICAgICAgICAgIGRpc2FibGVkPXtpc0xvYWRpbmcgfHwgYXZhaWxhYmxlQXBpS2V5cy5sZW5ndGggPT09IDB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtpc0xvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yIGFuaW1hdGUtc3BpblwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNCA0djVoLjU4Mm0xNS4zNTYgMkE4LjAwMSA4LjAwMSAwIDAwNC41ODIgOW0wIDBIOW0xMSAxMXYtNWgtLjU4MW0wIDBhOC4wMDMgOC4wMDMgMCAwMS0xNS4zNTctMm0xNS4zNTcgMkgxNVwiIC8+XG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgIFNhdmluZy4uLlxuICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGVJY29uIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICBTYXZlIFJvdXRpbmcgU2V0dGluZ3NcbiAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICk7XG4gICAgfVxuXG4gICAgaWYgKHNlbGVjdGVkU3RyYXRlZ3kgPT09ICdjb21wbGV4aXR5X3JvdW5kX3JvYmluJykge1xuICAgICAgcmV0dXJuIChcbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi00IGZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxDaXJjbGVTdGFja0ljb24gY2xhc3NOYW1lPVwidy03IGgtNyBtci0zIHRleHQtb3JhbmdlLTQwMFwiIC8+XG4gICAgICAgICAgICAgIENvbXBsZXhpdHktQmFzZWQgUm91bmQtUm9iaW5cbiAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICB7c2VsZWN0ZWRTdHJhdGVneURhdGE/LmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAge3JlbmRlckNvbXBsZXhpdHlBc3NpZ25tZW50VUkoKX1cblxuICAgICAgICAgIHsvKiBTYXZlIEJ1dHRvbiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTEyIHB0LTYgYm9yZGVyLXQgYm9yZGVyLWdyYXktNzAwLzUwIGZsZXgganVzdGlmeS1lbmRcIj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJ0bi1wcmltYXJ5IGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkXCJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzTG9hZGluZ31cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2lzTG9hZGluZyA/IChcbiAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTIgYW5pbWF0ZS1zcGluXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk00IDR2NWguNTgybTE1LjM1NiAyQTguMDAxIDguMDAxIDAgMDA0LjU4MiA5bTAgMEg5bTExIDExdi01aC0uNTgxbTAgMGE4LjAwMyA4LjAwMyAwIDAxLTE1LjM1Ny0ybTE1LjM1NyAySDE1XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgU2F2aW5nLi4uXG4gICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZUljb24gY2xhc3NOYW1lPVwidy00IGgtNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgIFNhdmUgUm91dGluZyBTZXR0aW5nc1xuICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKTtcbiAgICB9XG5cbiAgICBpZiAoc2VsZWN0ZWRTdHJhdGVneSA9PT0gJ2Nvc3Rfb3B0aW1pemVkJykge1xuICAgICAgcmV0dXJuIChcbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi00IGZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxDdXJyZW5jeURvbGxhckljb24gY2xhc3NOYW1lPVwidy03IGgtNyBtci0zIHRleHQtb3JhbmdlLTQwMFwiIC8+XG4gICAgICAgICAgICAgIFNtYXJ0IENvc3QtT3B0aW1pemVkIFJvdXRpbmdcbiAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICB7c2VsZWN0ZWRTdHJhdGVneURhdGE/LmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS05MDAvMjAgYm9yZGVyIGJvcmRlci1ibHVlLTUwMC8zMCByb3VuZGVkLXhsIHAtNiBiYWNrZHJvcC1ibHVyLXNtXCI+XG4gICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtYmx1ZS0zMDAgbWItM1wiPkhvdyBpdCB3b3Jrczo8L2g0PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMyB0ZXh0LXNtIHRleHQtYmx1ZS0yMDBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNiBoLTYgYmctYmx1ZS01MDAvMjAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG10LTAuNSBmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1ib2xkIHRleHQtYmx1ZS00MDBcIj4xPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8cD48c3Ryb25nPlJvdUtleSBDbGFzc2lmaWVyPC9zdHJvbmc+IGFuYWx5emVzIHlvdXIgcHJvbXB0IHRvIGRldGVybWluZSB0YXNrIGNvbXBsZXhpdHkgKFNpbXBsZSwgTW9kZXJhdGUsIENvbXBsZXgpPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy02IGgtNiBiZy1ibHVlLTUwMC8yMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXQtMC41IGZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyBmb250LWJvbGQgdGV4dC1ibHVlLTQwMFwiPjI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxwPlJvdXRlcyB0byBhcHByb3ByaWF0ZSBjb3N0IHRpZXI6IDxzdHJvbmc+U2ltcGxlIHRhc2tzPC9zdHJvbmc+IOKGkiBDaGVhcGVzdCBtb2RlbHMsIDxzdHJvbmc+Q29tcGxleCB0YXNrczwvc3Ryb25nPiDihpIgUHJlbWl1bSBtb2RlbHM8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTYgaC02IGJnLWJsdWUtNTAwLzIwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtdC0wLjUgZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNDAwXCI+Mzwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHA+UHJpb3JpdGl6ZXMgY29zdCBzYXZpbmdzIGJ5IHVzaW5nIGNoZWFwZXIgbW9kZWxzIHdoZW5ldmVyIHBvc3NpYmxlIHdoaWxlIGVuc3VyaW5nIHF1YWxpdHkgZm9yIGNvbXBsZXggdGFza3M8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMyBnYXAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyZWVuLTkwMC8yMCBib3JkZXIgYm9yZGVyLWdyZWVuLTUwMC8zMCByb3VuZGVkLXhsIHAtNCBiYWNrZHJvcC1ibHVyLXNtXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMgaC0zIGJnLWdyZWVuLTQwMCByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxoNSBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JlZW4tMzAwIHRleHQtc21cIj5TaW1wbGUgVGFza3M8L2g1PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmVlbi0yMDAgbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgICAgICBCYXNpYyBxdWVzdGlvbnMsIHNpbXBsZSBjb252ZXJzYXRpb25zLCBzdHJhaWdodGZvcndhcmQgcmVxdWVzdHMg4oaSIDxzdHJvbmc+Q2hlYXBlc3QgbW9kZWxzPC9zdHJvbmc+XG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXllbGxvdy05MDAvMjAgYm9yZGVyIGJvcmRlci15ZWxsb3ctNTAwLzMwIHJvdW5kZWQteGwgcC00IGJhY2tkcm9wLWJsdXItc21cIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMyBoLTMgYmcteWVsbG93LTQwMCByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxoNSBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQteWVsbG93LTMwMCB0ZXh0LXNtXCI+TW9kZXJhdGUgVGFza3M8L2g1PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC15ZWxsb3ctMjAwIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICAgICAgQW5hbHlzaXMsIGV4cGxhbmF0aW9ucywgbW9kZXJhdGUgY29tcGxleGl0eSDihpIgPHN0cm9uZz5CYWxhbmNlZCBwcmljaW5nIG1vZGVsczwvc3Ryb25nPlxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1wdXJwbGUtOTAwLzIwIGJvcmRlciBib3JkZXItcHVycGxlLTUwMC8zMCByb3VuZGVkLXhsIHAtNCBiYWNrZHJvcC1ibHVyLXNtXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMgaC0zIGJnLXB1cnBsZS00MDAgcm91bmRlZC1mdWxsXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LXB1cnBsZS0zMDAgdGV4dC1zbVwiPkNvbXBsZXggVGFza3M8L2g1PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1wdXJwbGUtMjAwIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICAgICAgQWR2YW5jZWQgcmVhc29uaW5nLCBjb2RpbmcsIHJlc2VhcmNoIOKGkiA8c3Ryb25nPlByZW1pdW0gbW9kZWxzPC9zdHJvbmc+XG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLW9yYW5nZS05MDAvMjAgYm9yZGVyIGJvcmRlci1vcmFuZ2UtNTAwLzMwIHJvdW5kZWQteGwgcC02IGJhY2tkcm9wLWJsdXItc21cIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgIDxDdXJyZW5jeURvbGxhckljb24gY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LW9yYW5nZS00MDAgbXQtMC41IGZsZXgtc2hyaW5rLTBcIiAvPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LW9yYW5nZS0zMDAgbWItMlwiPkludGVsbGlnZW50IENvc3QgT3B0aW1pemF0aW9uPC9oND5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1vcmFuZ2UtMjAwIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICAgICAgICBUaGlzIHN0cmF0ZWd5IG1heGltaXplcyBjb3N0IHNhdmluZ3MgYnkgcm91dGluZyBtb3N0IHJlcXVlc3RzIHRvIGNoZWFwZXIgbW9kZWxzLCB3aGlsZSBhdXRvbWF0aWNhbGx5IHVwZ3JhZGluZyB0byBwcmVtaXVtIG1vZGVscyBvbmx5IHdoZW4gdGFzayBjb21wbGV4aXR5IHRydWx5IHJlcXVpcmVzIGl0LiBQZXJmZWN0IGZvciBiYWxhbmNpbmcgYnVkZ2V0IGNvbnN0cmFpbnRzIHdpdGggcXVhbGl0eSByZXF1aXJlbWVudHMuXG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JlZW4tOTAwLzIwIGJvcmRlciBib3JkZXItZ3JlZW4tNTAwLzMwIHJvdW5kZWQteGwgcC02IGJhY2tkcm9wLWJsdXItc21cIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZUljb24gY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LWdyZWVuLTQwMCBtdC0wLjUgZmxleC1zaHJpbmstMFwiIC8+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JlZW4tMzAwIG1iLTJcIj5SZWFkeSB0byB1c2UhPC9oND5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmVlbi0yMDAgbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgICAgICAgIE5vIGFkZGl0aW9uYWwgc2V0dXAgcmVxdWlyZWQuIFJvdUtleSB3aWxsIGF1dG9tYXRpY2FsbHkgY2xhc3NpZnkgdGFzayBjb21wbGV4aXR5IGFuZCByb3V0ZSB0byB0aGUgbW9zdCBjb3N0LWVmZmVjdGl2ZSBtb2RlbCB0aWVyIGZvciBvcHRpbWFsIHNhdmluZ3MgYW5kIHBlcmZvcm1hbmNlLlxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFNhdmUgQnV0dG9uICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMTIgcHQtNiBib3JkZXItdCBib3JkZXItZ3JheS03MDAvNTAgZmxleCBqdXN0aWZ5LWVuZFwiPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLXByaW1hcnkgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7aXNMb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMiBhbmltYXRlLXNwaW5cIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTQgNHY1aC41ODJtMTUuMzU2IDJBOC4wMDEgOC4wMDEgMCAwMDQuNTgyIDltMCAwSDltMTEgMTF2LTVoLS41ODFtMCAwYTguMDAzIDguMDAzIDAgMDEtMTUuMzU3LTJtMTUuMzU3IDJIMTVcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICBTYXZpbmcuLi5cbiAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlSWNvbiBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgU2F2ZSBSb3V0aW5nIFNldHRpbmdzXG4gICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApO1xuICAgIH1cblxuICAgIGlmIChzZWxlY3RlZFN0cmF0ZWd5ID09PSAnYWJfcm91dGluZycpIHtcbiAgICAgIHJldHVybiAoXG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItNCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8QmVha2VySWNvbiBjbGFzc05hbWU9XCJ3LTcgaC03IG1yLTMgdGV4dC1vcmFuZ2UtNDAwXCIgLz5cbiAgICAgICAgICAgICAgQS9CIFJvdXRpbmcgT3B0aW1pemF0aW9uXG4gICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAge3NlbGVjdGVkU3RyYXRlZ3lEYXRhPy5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWJsdWUtOTAwLzIwIGJvcmRlciBib3JkZXItYmx1ZS01MDAvMzAgcm91bmRlZC14bCBwLTYgYmFja2Ryb3AtYmx1ci1zbVwiPlxuICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWJsdWUtMzAwIG1iLTNcIj5Ib3cgaXQgd29ya3M6PC9oND5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTMgdGV4dC1zbSB0ZXh0LWJsdWUtMjAwXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTYgaC02IGJnLWJsdWUtNTAwLzIwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtdC0wLjUgZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNDAwXCI+MTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHA+PHN0cm9uZz44NSUgQ29udHJvbCBHcm91cDo8L3N0cm9uZz4gUm91dGVzIHRvIHlvdXIgYmVzdC1wZXJmb3JtaW5nIG1vZGVscyBiYXNlZCBvbiBoaXN0b3JpY2FsIGRhdGE8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTYgaC02IGJnLWJsdWUtNTAwLzIwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtdC0wLjUgZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNDAwXCI+Mjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHA+PHN0cm9uZz4xNSUgVGVzdCBHcm91cDo8L3N0cm9uZz4gRXhwZXJpbWVudHMgd2l0aCBkaWZmZXJlbnQgbW9kZWxzIHRvIGRpc2NvdmVyIGJldHRlciBvcHRpb25zPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy02IGgtNiBiZy1ibHVlLTUwMC8yMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXQtMC41IGZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyBmb250LWJvbGQgdGV4dC1ibHVlLTQwMFwiPjM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxwPjxzdHJvbmc+Q29udGludW91cyBMZWFybmluZzo8L3N0cm9uZz4gQXV0b21hdGljYWxseSB1cGRhdGVzIHJvdXRpbmcgYmFzZWQgb24gcXVhbGl0eSBtZXRyaWNzIGFuZCB1c2VyIGZlZWRiYWNrPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi01MCBib3JkZXIgYm9yZGVyLWdyZWVuLTIwMCByb3VuZGVkLXhsIHAtNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0zIGgtMyBiZy1ncmVlbi01MDAgcm91bmRlZC1mdWxsXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWdyZWVuLTkwMCB0ZXh0LXNtXCI+UXVhbGl0eSBPcHRpbWl6YXRpb248L2g1PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmVlbi04MDAgbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgICAgICBUcmFja3MgcmVzcG9uc2UgcXVhbGl0eSwgdXNlciBzYXRpc2ZhY3Rpb24sIGFuZCB0YXNrIGNvbXBsZXRpb24gcmF0ZXMgdG8gaWRlbnRpZnkgdGhlIGJlc3QgbW9kZWxzIGZvciB5b3VyIHNwZWNpZmljIHVzZSBjYXNlcy5cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcHVycGxlLTUwIGJvcmRlciBib3JkZXItcHVycGxlLTIwMCByb3VuZGVkLXhsIHAtNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0zIGgtMyBiZy1wdXJwbGUtNTAwIHJvdW5kZWQtZnVsbFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGg1IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1wdXJwbGUtOTAwIHRleHQtc21cIj5Db3N0IEVmZmljaWVuY3k8L2g1PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1wdXJwbGUtODAwIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICAgICAgQmFsYW5jZXMgcXVhbGl0eSB3aXRoIGNvc3QgdG8gZmluZCBtb2RlbHMgdGhhdCBkZWxpdmVyIHRoZSBiZXN0IHZhbHVlIGZvciB5b3VyIHNwZWNpZmljIHJlcXVpcmVtZW50cyBhbmQgYnVkZ2V0LlxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1vcmFuZ2UtNTAgYm9yZGVyIGJvcmRlci1vcmFuZ2UtMjAwIHJvdW5kZWQteGwgcC02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICA8QmVha2VySWNvbiBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtb3JhbmdlLTYwMCBtdC0wLjUgZmxleC1zaHJpbmstMFwiIC8+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtb3JhbmdlLTkwMCBtYi0yXCI+SW50ZWxsaWdlbnQgRXhwZXJpbWVudGF0aW9uPC9oND5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1vcmFuZ2UtODAwIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICAgICAgICBUaGlzIHN0cmF0ZWd5IGNvbnRpbnVvdXNseSBsZWFybnMgZnJvbSB5b3VyIHVzYWdlIHBhdHRlcm5zIHRvIG9wdGltaXplIHJvdXRpbmcgZGVjaXNpb25zLiBUaGUgbW9yZSB5b3UgdXNlIGl0LCB0aGUgYmV0dGVyIGl0IGJlY29tZXMgYXQgc2VsZWN0aW5nIHRoZSBwZXJmZWN0IG1vZGVsIGZvciBlYWNoIHJlcXVlc3QuXG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JlZW4tNTAgYm9yZGVyIGJvcmRlci1ncmVlbi0yMDAgcm91bmRlZC14bCBwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZUljb24gY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LWdyZWVuLTYwMCBtdC0wLjUgZmxleC1zaHJpbmstMFwiIC8+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JlZW4tOTAwIG1iLTJcIj5SZWFkeSB0byB1c2UhPC9oND5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmVlbi04MDAgbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgICAgICAgIE5vIGFkZGl0aW9uYWwgc2V0dXAgcmVxdWlyZWQuIFJvdUtleSB3aWxsIGF1dG9tYXRpY2FsbHkgc3RhcnQgQS9CIHRlc3RpbmcgYW5kIGxlYXJuaW5nIGZyb20geW91ciByZXF1ZXN0cyB0byBvcHRpbWl6ZSByb3V0aW5nIHBlcmZvcm1hbmNlLlxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFNhdmUgQnV0dG9uICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMTIgcHQtNiBib3JkZXItdCBib3JkZXItZ3JheS0yMDAgZmxleCBqdXN0aWZ5LWVuZFwiPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLXByaW1hcnkgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7aXNMb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMiBhbmltYXRlLXNwaW5cIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTQgNHY1aC41ODJtMTUuMzU2IDJBOC4wMDEgOC4wMDEgMCAwMDQuNTgyIDltMCAwSDltMTEgMTF2LTVoLS41ODFtMCAwYTguMDAzIDguMDAzIDAgMDEtMTUuMzU3LTJtMTUuMzU3IDJIMTVcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICBTYXZpbmcuLi5cbiAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlSWNvbiBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgU2F2ZSBSb3V0aW5nIFNldHRpbmdzXG4gICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApO1xuICAgIH1cblxuICAgIHJldHVybiBudWxsO1xuICB9O1xuXG4gIC8vIE1haW4gcmVuZGVyIGxvZ2ljIHdpdGggb3B0aW1pc3RpYyBsb2FkaW5nXG4gIGlmIChzaG93T3B0aW1pc3RpY0xvYWRpbmcgJiYgIWlzQ2FjaGVkKGNvbmZpZ0lkKSkge1xuICAgIHJldHVybiA8Um91dGluZ1NldHVwTG9hZGluZ1NrZWxldG9uIC8+O1xuICB9XG5cbiAgaWYgKGlzTG9hZGluZyAmJiAhY29uZmlnRGV0YWlscykgeyAvLyBJbml0aWFsIGZ1bGwgbG9hZFxuICAgIHJldHVybiA8Q29tcGFjdFJvdXRpbmdTZXR1cExvYWRpbmdTa2VsZXRvbiAvPjtcbiAgfVxuXG4gIGlmIChlcnJvciAmJiAhY29uZmlnRGV0YWlscyAmJiAhaXNMb2FkaW5nKSB7IC8vIENyaXRpY2FsIGVycm9yLCBjYW5ub3QgbG9hZCBjb25maWdcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC00IHB5LThcIj5cbiAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTZcIj5Sb3V0aW5nIFNldHVwIEVycm9yPC9oMT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkIGJvcmRlci1yZWQtMjAwIGJnLXJlZC01MCBwLTZcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLXJlZC01MDAgcm91bmRlZC1mdWxsXCI+PC9kaXY+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC04MDBcIj57ZXJyb3J9PC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxMaW5rIGhyZWY9XCIvbXktbW9kZWxzXCIgY2xhc3NOYW1lPVwibXQtNCBidG4tcHJpbWFyeSBpbmxpbmUtYmxvY2tcIj5cbiAgICAgICAgICAgIEJhY2sgdG8gTXkgTW9kZWxzXG4gICAgICAgICAgPC9MaW5rPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cbiAgXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctWyMwNDA3MTZdIHRleHQtd2hpdGVcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNiBweS04XCI+XG4gICAgICAgIHsvKiBDbGVhbiBIZWFkZXIgU2VjdGlvbiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVCYWNrTmF2aWdhdGlvbn1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1zbSBweC00IHB5LTIgYmctZ3JheS04MDAvNTAgaG92ZXI6YmctZ3JheS03MDAvNTAgdGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ncmF5LTcwMC81MCBob3Zlcjpib3JkZXItZ3JheS02MDAvNTAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxBcnJvd0xlZnRJY29uIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAge2dldEJhY2tCdXR0b25UZXh0KCl9XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cbiAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtaDEgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgIEFkdmFuY2VkIFJvdXRpbmcgU2V0dXBcbiAgICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgICAge2NvbmZpZ0RldGFpbHMgJiYgKFxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtYm9keS1zbSB0ZXh0LWdyYXktNDAwIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgIENvbmZpZ3VyYXRpb246IDxzcGFuIGNsYXNzTmFtZT1cInRleHQtb3JhbmdlLTQwMCBmb250LXNlbWlib2xkXCI+e2NvbmZpZ0RldGFpbHMubmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBTdGF0dXMgTWVzc2FnZXMgKi99XG4gICAgICAgIHsoZXJyb3IgJiYgIXN1Y2Nlc3NNZXNzYWdlKSAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtOTAwLzIwIGJvcmRlciBib3JkZXItcmVkLTUwMC8zMCByb3VuZGVkLXhsIHAtNiBtYi04IG1heC13LTR4bCBteC1hdXRvIGJhY2tkcm9wLWJsdXItc21cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1yZWQtNTAwLzIwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXJlZC00MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMiA4djRtMCA0aC4wMU0yMSAxMmE5IDkgMCAxMS0xOCAwIDkgOSAwIDAxMTggMHpcIiAvPlxuICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LXJlZC0zMDBcIj5Db25maWd1cmF0aW9uIEVycm9yPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC00MDAgbXQtMVwiPntlcnJvcn08L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAge3N1Y2Nlc3NNZXNzYWdlICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyZWVuLTkwMC8yMCBib3JkZXIgYm9yZGVyLWdyZWVuLTUwMC8zMCByb3VuZGVkLXhsIHAtNiBtYi04IG1heC13LTR4bCBteC1hdXRvIGJhY2tkcm9wLWJsdXItc21cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1ncmVlbi01MDAvMjAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JlZW4tNDAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNSAxM2w0IDRMMTkgN1wiIC8+XG4gICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JlZW4tMzAwXCI+U2V0dGluZ3MgU2F2ZWQ8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNDAwIG10LTFcIj57c3VjY2Vzc01lc3NhZ2V9PC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBNYWluIFR3by1QYW5lbCBMYXlvdXQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG9cIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTMgZ2FwLThcIj5cbiAgICAgICAgICAgIHsvKiBMZWZ0IFBhbmVsIC0gU3RyYXRlZ3kgQ2FyZHMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTFcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMC81MCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItZ3JheS04MDAvNTAgcm91bmRlZC14bCBwLTYgc3RpY2t5IHRvcC04XCI+XG4gICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItNlwiPlJvdXRpbmcgU3RyYXRlZ3k8L2gyPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgICB7Uk9VVElOR19TVFJBVEVHSUVTLm1hcCgoc3RyYXRlZ3kpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgSWNvbkNvbXBvbmVudCA9IHN0cmF0ZWd5Lmljb247XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGlzU2VsZWN0ZWQgPSBzZWxlY3RlZFN0cmF0ZWd5ID09PSBzdHJhdGVneS5pZDtcblxuICAgICAgICAgICAgICAgICAgICAvLyBEZWZpbmUgd2hpY2ggc3RyYXRlZ2llcyByZXF1aXJlIGFkdmFuY2VkIHJvdXRpbmcgYWNjZXNzXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGFkdmFuY2VkU3RyYXRlZ2llcyA9IFsnaW50ZWxsaWdlbnRfcm9sZScsICdjb21wbGV4aXR5X3JvdW5kX3JvYmluJywgJ2F1dG9fb3B0aW1hbCcsICdjb3N0X29wdGltaXplZCcsICdhYl9yb3V0aW5nJ107XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGlzQWR2YW5jZWQgPSBhZHZhbmNlZFN0cmF0ZWdpZXMuaW5jbHVkZXMoc3RyYXRlZ3kuaWQpO1xuXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHN0cmF0ZWd5QnV0dG9uID0gKFxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIGtleT17c3RyYXRlZ3kuaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkU3RyYXRlZ3koc3RyYXRlZ3kuaWQpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBSZXNldC9pbml0aWFsaXplIHBhcmFtcyBiYXNlZCBvbiBuZXcgc3RyYXRlZ3lcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHN0cmF0ZWd5LmlkID09PSAnc3RyaWN0X2ZhbGxiYWNrJykge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGV4aXN0aW5nRmFsbGJhY2tQYXJhbXMgPSBzdHJhdGVneVBhcmFtcy5vcmRlcmVkX2FwaV9rZXlfaWRzIGFzIHN0cmluZ1tdO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChleGlzdGluZ0ZhbGxiYWNrUGFyYW1zICYmIEFycmF5LmlzQXJyYXkoZXhpc3RpbmdGYWxsYmFja1BhcmFtcykpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlb3JkZXJlZCA9IGV4aXN0aW5nRmFsbGJhY2tQYXJhbXMubWFwKGlkID0+IGF2YWlsYWJsZUFwaUtleXMuZmluZChrID0+IGsuaWQgPT09IGlkKSkuZmlsdGVyKEJvb2xlYW4pIGFzIERpc3BsYXlBcGlLZXlbXTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlbWFpbmluZyA9IGF2YWlsYWJsZUFwaUtleXMuZmlsdGVyKGsgPT4gIWV4aXN0aW5nRmFsbGJhY2tQYXJhbXMuaW5jbHVkZXMoay5pZCkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0T3JkZXJlZEZhbGxiYWNrS2V5cyhbLi4ucmVvcmRlcmVkLCAuLi5yZW1haW5pbmddKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0T3JkZXJlZEZhbGxiYWNrS2V5cyhbLi4uYXZhaWxhYmxlQXBpS2V5c10pO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRTdHJhdGVneVBhcmFtcyh7IG9yZGVyZWRfYXBpX2tleV9pZHM6IG9yZGVyZWRGYWxsYmFja0tleXMubWFwKGsgPT4gay5pZCkgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0U3RyYXRlZ3lQYXJhbXMoe30pO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldE9yZGVyZWRGYWxsYmFja0tleXMoWy4uLmF2YWlsYWJsZUFwaUtleXNdKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBSZXNldCBjb21wbGV4aXR5IGFzc2lnbm1lbnQgc3RhdGVzIGlmIHN0cmF0ZWd5IGNoYW5nZXNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRBcGlLZXlGb3JDb21wbGV4aXR5KG51bGwpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRDdXJyZW50S2V5Q29tcGxleGl0eUxldmVscyhbXSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNldEFzc2lnbm1lbnRzRXJyb3IobnVsbCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNldEFzc2lnbm1lbnRzU3VjY2Vzc01lc3NhZ2UobnVsbCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzTG9hZGluZ31cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCB0ZXh0LWxlZnQgcC00IHJvdW5kZWQteGwgYm9yZGVyLTIgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGdyb3VwICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGlzU2VsZWN0ZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdib3JkZXItb3JhbmdlLTUwMCBiZy1vcmFuZ2UtNTAwLzEwIHNoYWRvdy1sZyB0cmFuc2Zvcm0gc2NhbGUtWzEuMDJdJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci1ncmF5LTcwMC81MCBiZy1ncmF5LTgwMC8zMCBob3Zlcjpib3JkZXItb3JhbmdlLTQwMC81MCBob3ZlcjpiZy1vcmFuZ2UtNTAwLzUgaG92ZXI6c2hhZG93LW1kJ1xuICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHAtMyByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzU2VsZWN0ZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLW9yYW5nZS01MDAvMjAgdGV4dC1vcmFuZ2UtNDAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnYmctZ3JheS03MDAvNTAgdGV4dC1ncmF5LTQwMCBncm91cC1ob3ZlcjpiZy1vcmFuZ2UtNTAwLzEwIGdyb3VwLWhvdmVyOnRleHQtb3JhbmdlLTQwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJY29uQ29tcG9uZW50IGNsYXNzTmFtZT1cInctNiBoLTZcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9e2Bmb250LXNlbWlib2xkIHRleHQtc20gdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMzAwICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzU2VsZWN0ZWQgPyAndGV4dC1vcmFuZ2UtMzAwJyA6ICd0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c3RyYXRlZ3kubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXNTZWxlY3RlZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZUljb24gY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LW9yYW5nZS00MDAgYW5pbWF0ZS1pbiBmYWRlLWluIGR1cmF0aW9uLTMwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT17YHRleHQteHMgbGVhZGluZy1yZWxheGVkIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNTZWxlY3RlZCA/ICd0ZXh0LW9yYW5nZS00MDAvODAnIDogJ3RleHQtZ3JheS00MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3N0cmF0ZWd5LnNob3J0RGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICApO1xuXG4gICAgICAgICAgICAgICAgICAgIC8vIFdyYXAgYWR2YW5jZWQgc3RyYXRlZ2llcyB3aXRoIFRpZXJHdWFyZFxuICAgICAgICAgICAgICAgICAgICBpZiAoaXNBZHZhbmNlZCkge1xuICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8VGllckd1YXJkXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17c3RyYXRlZ3kuaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGZlYXR1cmU9XCJhZHZhbmNlZF9yb3V0aW5nXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY3VzdG9tTWVzc2FnZT17YCR7c3RyYXRlZ3kubmFtZX0gaXMgYXZhaWxhYmxlIHN0YXJ0aW5nIHdpdGggdGhlIFN0YXJ0ZXIgcGxhbi4gVXBncmFkZSB0byBhY2Nlc3MgYWR2YW5jZWQgcm91dGluZyBzdHJhdGVnaWVzIHRoYXQgb3B0aW1pemUgcGVyZm9ybWFuY2UgYW5kIGNvc3QuYH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgZmFsbGJhY2s9e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib3BhY2l0eS01MCBwb2ludGVyLWV2ZW50cy1ub25lXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzdHJhdGVneUJ1dHRvbn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS05MDAvOTAgYmFja2Ryb3AtYmx1ci1zbSBib3JkZXIgYm9yZGVyLW9yYW5nZS01MDAvMzAgcm91bmRlZC1sZyBweC0zIHB5LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtb3JhbmdlLTQwMFwiPlN0YXJ0ZXIgUGxhbiBSZXF1aXJlZDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7c3RyYXRlZ3lCdXR0b259XG4gICAgICAgICAgICAgICAgICAgICAgICA8L1RpZXJHdWFyZD5cbiAgICAgICAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHN0cmF0ZWd5QnV0dG9uO1xuICAgICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBSaWdodCBQYW5lbCAtIENvbmZpZ3VyYXRpb24gT3B0aW9ucyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6Y29sLXNwYW4tMlwiPlxuICAgICAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU2F2ZVJvdXRpbmdTZXR0aW5nc30+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMC81MCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItZ3JheS04MDAvNTAgcm91bmRlZC14bCBwLTggbWluLWgtWzYwMHB4XVwiPlxuICAgICAgICAgICAgICAgICAgey8qIENvbmZpZ3VyYXRpb24gQ29udGVudCB3aXRoIEFuaW1hdGlvbiAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1pbiBmYWRlLWluIHNsaWRlLWluLWZyb20tcmlnaHQtNCBkdXJhdGlvbi01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge3JlbmRlckNvbmZpZ3VyYXRpb25Db250ZW50KCl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9mb3JtPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59Il0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJ1c2VQYXJhbXMiLCJ1c2VSb3V0ZXIiLCJ1c2VTZWFyY2hQYXJhbXMiLCJMaW5rIiwiQXJyb3dMZWZ0SWNvbiIsIkFycm93VXBJY29uIiwiQXJyb3dEb3duSWNvbiIsIkNvZzZUb290aEljb24iLCJCb2x0SWNvbiIsIkNpcmNsZVN0YWNrSWNvbiIsIkxpc3RCdWxsZXRJY29uIiwiQ2hlY2tDaXJjbGVJY29uIiwiQ3VycmVuY3lEb2xsYXJJY29uIiwiQmFyczNJY29uIiwiQmVha2VySWNvbiIsIlJlb3JkZXIiLCJ1c2VSb3V0aW5nU2V0dXBQcmVmZXRjaCIsIlJvdXRpbmdTZXR1cExvYWRpbmdTa2VsZXRvbiIsIkNvbXBhY3RSb3V0aW5nU2V0dXBMb2FkaW5nU2tlbGV0b24iLCJUaWVyR3VhcmQiLCJST1VUSU5HX1NUUkFURUdJRVMiLCJpZCIsIm5hbWUiLCJzaG9ydERlc2NyaXB0aW9uIiwiZGVzY3JpcHRpb24iLCJpY29uIiwiRHJhZ2dhYmxlQXBpS2V5SXRlbSIsImFwaUtleSIsImluZGV4IiwiSXRlbSIsInZhbHVlIiwiY2xhc3NOYW1lIiwid2hpbGVEcmFnIiwic2NhbGUiLCJib3hTaGFkb3ciLCJ6SW5kZXgiLCJkcmFnTGlzdGVuZXIiLCJkcmFnQ29udHJvbHMiLCJ1bmRlZmluZWQiLCJkaXYiLCJzcGFuIiwibGFiZWwiLCJwIiwicHJvdmlkZXIiLCJwcmVkZWZpbmVkX21vZGVsX2lkIiwiU2ltcGxlQXBpS2V5SXRlbSIsIm9uTW92ZVVwIiwib25Nb3ZlRG93biIsImxpIiwiYnV0dG9uIiwib25DbGljayIsInRpdGxlIiwiUm91dGluZ1NldHVwQ29uZmlnUGFnZSIsInBhcmFtcyIsInJvdXRlciIsInNlYXJjaFBhcmFtcyIsImNvbmZpZ0lkIiwiZ2V0Q2FjaGVkRGF0YSIsImlzQ2FjaGVkIiwiZ2V0QmFja1VybCIsImZyb21QYXJhbSIsImdldCIsInJlZmVycmVyIiwiZG9jdW1lbnQiLCJjdXJyZW50SG9zdCIsIndpbmRvdyIsImxvY2F0aW9uIiwiaG9zdCIsImluY2x1ZGVzIiwicmVmZXJyZXJVcmwiLCJVUkwiLCJyZWZlcnJlclBhdGgiLCJwYXRobmFtZSIsInN0YXJ0c1dpdGgiLCJlIiwiZ2V0QmFja0J1dHRvblRleHQiLCJoYW5kbGVCYWNrTmF2aWdhdGlvbiIsImhpc3RvcnkiLCJsZW5ndGgiLCJiYWNrVXJsIiwiYmFjayIsInB1c2giLCJjb25maWdEZXRhaWxzIiwic2V0Q29uZmlnRGV0YWlscyIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJzdWNjZXNzTWVzc2FnZSIsInNldFN1Y2Nlc3NNZXNzYWdlIiwic2hvd09wdGltaXN0aWNMb2FkaW5nIiwic2V0U2hvd09wdGltaXN0aWNMb2FkaW5nIiwic2VsZWN0ZWRTdHJhdGVneSIsInNldFNlbGVjdGVkU3RyYXRlZ3kiLCJzdHJhdGVneVBhcmFtcyIsInNldFN0cmF0ZWd5UGFyYW1zIiwiYXZhaWxhYmxlQXBpS2V5cyIsInNldEF2YWlsYWJsZUFwaUtleXMiLCJpc0xvYWRpbmdBcGlLZXlzIiwic2V0SXNMb2FkaW5nQXBpS2V5cyIsIm9yZGVyZWRGYWxsYmFja0tleXMiLCJzZXRPcmRlcmVkRmFsbGJhY2tLZXlzIiwic2VsZWN0ZWRBcGlLZXlGb3JDb21wbGV4aXR5Iiwic2V0U2VsZWN0ZWRBcGlLZXlGb3JDb21wbGV4aXR5IiwiY29tcGxleGl0eUFzc2lnbm1lbnRzIiwic2V0Q29tcGxleGl0eUFzc2lnbm1lbnRzIiwiY3VycmVudEtleUNvbXBsZXhpdHlMZXZlbHMiLCJzZXRDdXJyZW50S2V5Q29tcGxleGl0eUxldmVscyIsImlzRmV0Y2hpbmdBc3NpZ25tZW50cyIsInNldElzRmV0Y2hpbmdBc3NpZ25tZW50cyIsImlzU2F2aW5nQXNzaWdubWVudHMiLCJzZXRJc1NhdmluZ0Fzc2lnbm1lbnRzIiwiYXNzaWdubWVudHNFcnJvciIsInNldEFzc2lnbm1lbnRzRXJyb3IiLCJhc3NpZ25tZW50c1N1Y2Nlc3NNZXNzYWdlIiwic2V0QXNzaWdubWVudHNTdWNjZXNzTWVzc2FnZSIsImZldGNoQ29uZmlnQW5kS2V5cyIsImNhY2hlZERhdGEiLCJhcGlLZXlzIiwiY29uc29sZSIsImxvZyIsImN1cnJlbnRTZWxlY3RlZFN0cmF0ZWd5Iiwicm91dGluZ1N0cmF0ZWd5Iiwicm91dGluZ1BhcmFtcyIsIm9yZGVyZWRfYXBpX2tleV9pZHMiLCJzYXZlZE9yZGVyIiwicmVvcmRlcmVkS2V5cyIsIm1hcCIsImZpbmQiLCJrIiwiZmlsdGVyIiwiQm9vbGVhbiIsInJlbWFpbmluZ0tleXMiLCJjb25maWdSZXMiLCJmZXRjaCIsIm9rIiwiZXJyRGF0YSIsImpzb24iLCJFcnJvciIsImN1cnJlbnRDb25maWciLCJyb3V0aW5nX3N0cmF0ZWd5IiwiY3VycmVudFN0cmF0ZWd5UGFyYW1zIiwicm91dGluZ19zdHJhdGVneV9wYXJhbXMiLCJrZXlzUmVzIiwia2V5cyIsImVyciIsIm1lc3NhZ2UiLCJmZXRjaENvbXBsZXhpdHlBc3NpZ25tZW50c0ZvcktleSIsImFwaUtleUlkIiwicmVzcG9uc2UiLCJlcnJvckRhdGEiLCJmZXRjaGVkTGV2ZWxzIiwicHJldiIsImhhbmRsZUNvbXBsZXhpdHlMZXZlbENoYW5nZSIsImxldmVsIiwiY2hlY2tlZCIsInByZXZMZXZlbHMiLCJzb3J0IiwiYSIsImIiLCJsIiwiaGFuZGxlU2F2ZUNvbXBsZXhpdHlBc3NpZ25tZW50cyIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsImNvbXBsZXhpdHlfbGV2ZWxzIiwicmVzdWx0IiwiaGFuZGxlRHJhZ1Jlb3JkZXIiLCJuZXdPcmRlciIsIm1vdmVGYWxsYmFja0tleSIsImRpcmVjdGlvbiIsIm5ld09yZGVyZWRLZXlzIiwia2V5VG9Nb3ZlIiwic3BsaWNlIiwiaGFuZGxlU2F2ZVJvdXRpbmdTZXR0aW5ncyIsInByZXZlbnREZWZhdWx0IiwicGFyYW1zVG9TYXZlIiwicmVuZGVyQ29tcGxleGl0eUFzc2lnbm1lbnRVSSIsImgzIiwiaHRtbEZvciIsInNlbGVjdCIsIm9uQ2hhbmdlIiwidGFyZ2V0Iiwib3B0aW9uIiwiZGlzYWJsZWQiLCJrZXkiLCJoNCIsImlucHV0IiwidHlwZSIsInJlbmRlckNvbmZpZ3VyYXRpb25Db250ZW50Iiwic2VsZWN0ZWRTdHJhdGVneURhdGEiLCJzIiwic3ZnIiwiZmlsbCIsInN0cm9rZSIsInZpZXdCb3giLCJwYXRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiLCJkIiwiR3JvdXAiLCJheGlzIiwidmFsdWVzIiwib25SZW9yZGVyIiwic3Ryb25nIiwiaDUiLCJoMSIsImhyZWYiLCJoMiIsInN0cmF0ZWd5IiwiSWNvbkNvbXBvbmVudCIsImlzU2VsZWN0ZWQiLCJhZHZhbmNlZFN0cmF0ZWdpZXMiLCJpc0FkdmFuY2VkIiwic3RyYXRlZ3lCdXR0b24iLCJleGlzdGluZ0ZhbGxiYWNrUGFyYW1zIiwiQXJyYXkiLCJpc0FycmF5IiwicmVvcmRlcmVkIiwicmVtYWluaW5nIiwiZmVhdHVyZSIsImN1c3RvbU1lc3NhZ2UiLCJmYWxsYmFjayIsImZvcm0iLCJvblN1Ym1pdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/routing-setup/[configId]/page.tsx\n"));

/***/ })

});